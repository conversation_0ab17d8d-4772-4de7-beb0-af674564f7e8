import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface CauHinhTyLeHoaHongContextProps {
  danhSachDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  loading: boolean;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang;
  tongSoDong: number;
  danhSachTyLeHoaHong: Array<CommonExecute.Execute.ICauHinhTyLeHoaHong>;
  cayTyLeHoaHong: Array<CommonExecute.Execute.ICayTyLeHoaHong>;
  setCayTyLeHoaHong: (data: Array<CommonExecute.Execute.ICayTyLeHoaHong>) => void;
  layCayTyLeHoaHong: (params: ReactQuery.ILayCayTyLeHoaHongParams) => Promise<Array<CommonExecute.Execute.ICayTyLeHoaHong>>;
  setDanhSachTyLeHoaHong: (data: Array<CommonExecute.Execute.ICauHinhTyLeHoaHong>) => void;
  layDanhSachTyLeHoaHong: (params: ReactQuery.ILietKeDanhSachTyLeHoaHongParams) => void;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang>>;
  getListDoiTac: () => Promise<void>;
  layDanhSachDaiLyPhanTrang: (params: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams) => Promise<{data: Array<CommonExecute.Execute.IDanhMucDaiLy>; tong_so_dong: number} | null>;
  layChiTietDaiLy: (params: ReactQuery.IChiTietDanhMucDaiLyParams) => Promise<CommonExecute.Execute.IChiTietDanhMucDaiLy | null>;
  onUpdateCauHinhTyLeHoaHong: (item: ReactQuery.ICapNhatCauHinhTyLeHoaHongParams) => void;
  // defaultFormValue: object;
}

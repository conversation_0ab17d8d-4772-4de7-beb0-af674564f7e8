import {createContext, useContext} from "react";
import {DanhMucDaiLyContextProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const DanhMucDaiLyContext = createContext<DanhMucDaiLyContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachDaiLy: [],
  listDoiTac: [],
  getListDoiTac: () => Promise.resolve(),
  loading: false,
  layDanhSachDaiLyPhanTrang: () => Promise.resolve({data: [], tong_so_dong: 0}),
  tongSoDong: 0,
  layChiTietDaiLy: params => Promise.resolve(null),
  filterParams: {},
  setFilterParams: () => {},
  onUpdateDanhMucDaiLy: () => Promise.resolve(null),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useDanhMucDaiLyContext = () => useContext(DanhMucDaiLyContext);

{
  "compilerOptions": {
    "target": "ESNext", //Biên dịch về chuẩn ECMAScript mới nhất
    "lib": ["DOM", "DOM.Iterable", "ESNext"], //<PERSON><PERSON> gồm định nghĩa kiểu cho Web API (DOM), đối tượng có thể lặp (DOM.Iterable), và toàn bộ tính năng mới của JS
    "module": "ESNext", //Dùng định dạng module mới nhất (tương thích Vite, ESM).
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler", // Vite yêu cầu mode bundler để xử lý module theo cách riêng của nó (tốt hơn node).
    "allowImportingTsExtensions": true, // Cho phép import file .ts, .tsx kèm đuôi mở rộng, ví dụ: import x from './file.ts'
    "resolveJsonModule": true, //Cho phép import file .json trong TypeScript: import config from './config.json'.
    "isolatedModules": true, //  Yêu cầu mỗi file phải là module độc lập – cần thiết cho build không dùng tsc, như Vite, Babel, etc.
    "noEmit": true, // Không xuất ra file .js sau khi biên dịch, vì Vite sẽ xử lý việc đó.
    "jsx": "react-jsx", // Sử dụng JSX theo chuẩn React 17+ (không cần import React from 'react' nữa).

    /* Linting */
    "strict": true, //  Bật tất cả các kiểm tra nghiêm ngặt (noImplicitAny, strictNullChecks,...).
    "noUnusedLocals": false, //  Cho phép khai báo biến/tham số không dùng mà không bị lỗi khi build (vite build không báo lỗi is declared but its value is never read).
    "noUnusedParameters": false, //  Cho phép khai báo biến/tham số không dùng mà không bị lỗi khi build (vite build không báo lỗi is declared but its value is never read).
    "noFallthroughCasesInSwitch": true, //  Ngăn lỗi rơi xuống case tiếp theo trong switch/case nếu không có break.
    "baseUrl": "./", // Gốc của alias là thư mục hiện tại (root project).
    //Thiết lập các alias giúp import ngắn gọn hơn,
    "paths": {
      "@src/*": ["./src/*"],
      "@R": ["./src/assets"],
      "@components/*": ["./src/components/*"],
      "@configs/*": ["./src/configs/*"],
      "@constants/*": ["./src/constants/*"],
      "@pages/*": ["./src/pages/*"],
      "@utils/*": ["./src/utils/*"],
      "@services/*": ["./src/services/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@i18n/*": ["./src/configs/i18n/*"]
    },
    "types": ["node"], // Tự động thêm định nghĩa kiểu của Node.js.
    "typeRoots": ["./node_modules/@types", "./src/@types", "@testing-library/jest-dom"] // Định nghĩa nơi TypeScript tìm các file .d.ts (kiểu mở rộng).
  },
  // các file/folder được biên dịch
  "include": ["src", "src/**/*.ts", "src/**/*.tsx", "src/@types/**/*.d.ts", "*/**.tsx"], // Chỉ định thư mục và file sẽ được compiler xử lý.
  "references": [{"path": "./tsconfig.node.json"}]
}

import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiMucDoTonThatXe, TRANG_THAI_TAO_MOI_MUC_DO_TON_THAT_XE} from "../index.configs";
import {useQuanLyMucDoTonThatXeContext} from "../index.context";
import {ChiTietMucDoTonThatXeProps, IModalChiTietMucDoTonThatXeRef} from "./Constant";
const {ma, ten, stt, trang_thai} = FormTaoMoiMucDoTonThatXe;

const ModalChiTietMucDoTonThatXeComponent = forwardRef<IModalChiTietMucDoTonThatXeRef, ChiTietMucDoTonThatXeProps>(({listMucDoTonThatXe}: ChiTietMucDoTonThatXeProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataMucDoTonThatXe?: CommonExecute.Execute.IMucDoTonThatXe) => {
      setIsOpen(true);
      if (dataMucDoTonThatXe) setChiTietMucDoTonThatXe(dataMucDoTonThatXe); // nếu có dữ liệu -> set chi tiết MucDoTonThatXe -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietMucDoTonThatXe, setChiTietMucDoTonThatXe] = useState<CommonExecute.Execute.IMucDoTonThatXe | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietMucDoTonThatXe, getListMucDoTonThatXe, loading} = useQuanLyMucDoTonThatXeContext();

  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietMucDoTonThatXe) {
      const arrFormData = [];
      for (const key in chiTietMucDoTonThatXe) {
        arrFormData.push({
          name: key,
          value: chiTietMucDoTonThatXe[key as keyof CommonExecute.Execute.IMucDoTonThatXe],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietMucDoTonThatXe]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietMucDoTonThatXe(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatMucDoTonThatXeParams = form.getFieldsValue(); //lấy ra values của form
      //với trường hợp tạo mới -> check mã MucDoTonThatXe đã tồn tại
      // if (!chiTietMucDoTonThatXe) {
      //   for (let i = 0; i < listMucDoTonThatXe.length; i++) {
      //     if (listMucDoTonThatXe[i].ma === values.ma) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã MucDoTonThatXe đã tổn tại!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      await capNhatChiTietMucDoTonThatXe(values); //cập nhật lại MucDoTonThatXe
      await getListMucDoTonThatXe(); //lấy lại danh sách MucDoTonThatXe
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span: number = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietMucDoTonThatXe ? true : false}, 8)}
        {renderFormColum({...ten}, 16)}
      </Row>

      <Row gutter={16}>
        {renderFormColum({...stt}, 8)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_MUC_DO_TON_THAT_XE}, 16)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietMucDoTonThatXe ? `${chiTietMucDoTonThatXe.ten}` : "Tạo mới mức độ tổn thất xe"} trang_thai_ten={chiTietMucDoTonThatXe?.trang_thai_ten} trang_thai={chiTietMucDoTonThatXe?.trang_thai} />}
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={600}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietMucDoTonThatXeComponent.displayName = "ModalChiTietMucDoTonThatXeComponent";
export const ModalChiTietMucDoTonThatXe = memo(ModalChiTietMucDoTonThatXeComponent, isEqual);

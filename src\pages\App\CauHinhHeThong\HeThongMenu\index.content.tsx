import {ClearOutlined, PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";

import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import type {FilterDropdownProps} from "antd/es/table/interface";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";

import {FormTimKiemHeThongMenu, heThongMenuColumns, TableHeThongMenuDataType, radioItemTrangThaiMenuTable, TRANG_THAI_MENU, NHOM_MENU} from "./index.configs";
import {useHeThongMenuContext} from "./index.context"; // file này lưu biến về state
import "./index.default.scss";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {ModalThemHeThongMenu} from "./component/ModalThemHeThongMenu";
import {IModalThemHeThongMenuRef} from "./component/index.configs";

type DataIndex = keyof TableHeThongMenuDataType;
/*
file LoginContent : xử lý phần UI của màn hình
React.Fc : khai báo rằng type LoginContent là 1 React Function
*/

const PAGE_SIZE = defaultPaginationTableProps.defaultPageSize;

const HeThongMenuContent: React.FC = () => {
  const {loading, tongSoDong, filterParams, setFilterParams, danhSachMenu, layDanhSachMenuPhanTrang, layChiTietMenu} = useHeThongMenuContext();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimkiemPhanTrangHeThongMenuParams>(filterParams);
  const [page, setPage] = useState(1);

  const refModalThemHeThongMenu = useRef<IModalThemHeThongMenuRef>(null);
  const {ma, ten, nhom, trang_thai} = FormTimKiemHeThongMenu;
  //   const listMenu = useState<CommonExecute.Execute.IHeThongMenu | null>(null);
  const dataTableListMenu = useMemo<Array<TableHeThongMenuDataType>>(() => {
    try {
      const tableData = danhSachMenu.map((item: any, index: number) => {
        return {
          key: index.toString(),
          // stt: item.stt ?? index + 1,
          stt: item.stt,
          ma: item.ma,
          ten: item.ten,
          ma_cha: item.ma_cha,
          url: item.url,
          nhom: item.nhom,
          sott: item.sott,
          ngay_tao: item.ngay_tao,
          nguoi_tao: item.nguoi_tao,
          ngay_cap_nhat: item.ngay_cap_nhat,
          nguoi_cap_nhat: item.nguoi_cap_nhat,
          trang_thai: item.trang_thai,
          trang_thai_ten: item.trang_thai_ten,
        };
      });

      const arrEmptyRow: Array<TableHeThongMenuDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListMenu error", error);
      return [];
    }
  }, [danhSachMenu]);
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  //Bấm tìm kiếm
  const onSearchApi = (values: ReactQuery.ITimkiemPhanTrangHeThongMenuParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma: values.ma ?? "",
      ten: values.ten ?? "",
      nhom: values.nhom ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong,
    };
    console.log("tongSoDong", tongSoDong);
    setSearchParams(cleanedValues);
    setPage(1); // reset về trang 1 khi search mới
    setFilterParams({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
    console.log("filterparam conten", filterParams);

    // layDanhSachMenuPhanTrang({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
  };

  //Gọi api khi chuyển trang
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      setPage(page);
      setFilterParams({...filterParams, trang: page, so_dong: PAGE_SIZE});
      // layDanhSachMenuPhanTrang({...searchParams, trang: page, so_dong: PAGE_SIZE});
    },
    [searchParams],
  );
  //Đồng bộ chức năng
  // RENDER
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableHeThongMenuDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiMenuTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      ); // xử lý chuyển text thành 1 dòng khi text quá dài;
    },
  });

  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //render header table
  const renderHeaderTableHeThongMenu = () => {
    return (
      <div>
        <Form initialValues={{trang_thai: TRANG_THAI_MENU[0].ma}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div className="flex flex-col gap-5">
            <Row gutter={16} align={"bottom"}>
              {renderFormInputColum({...ma})}

              {renderFormInputColum(ten)}
              {renderFormInputColum({...nhom, options: NHOM_MENU})}
              {renderFormInputColum({...trang_thai, options: TRANG_THAI_MENU})}
              <Col span={3}>
                {/* <Form.Item> */}
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="mb-0 w-full">
                  Tìm kiếm
                </Button>
                {/* </Form.Item> */}
              </Col>
              <Col span={3}>
                {/* <Form.Item>
                  <Flex wrap="wrap" gap="small" className="w-full"> */}
                <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalThemHeThongMenu.current?.open()} loading={loading}>
                  Tạo mới
                </Button>
                {/* </Flex>
                </Form.Item> */}
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    );
  };

  //render table
  return (
    <div id={ID_PAGE.HE_THONG_MENU} className="[&_.ant-space]:w-full">
      <Table<TableHeThongMenuDataType>
        {...defaultTableProps}
        // className="antd-table-hide-scroll"

        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const chiTietMenu = await layChiTietMenu({ma: record.ma});
              if (chiTietMenu) {
                refModalThemHeThongMenu.current?.open(chiTietMenu);
              }
            },
          };
        }}
        columns={(heThongMenuColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableHeThongMenuDataType, item.title) : {}),
          };
        })} //định nghĩa cột của table
        dataSource={dataTableListMenu}
        title={renderHeaderTableHeThongMenu}
        pagination={{
          ...defaultPaginationTableProps,

          current: page,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
          // showLessItems: true, //Hiển thị ít mục trang hơn
          // defaultPageSize: 2, //Số lượng item dữ liệu mặc định trên mỗi trang
          // onShowSizeChange : Được gọi khi pageSize thay đổi
          // showTitle: false,// Hiển thị tiêu đề của item trang
          //showTotal : hiển thị UI bao nhiêu / bao nhiêu bản ghi
        }}
      />
      <ModalThemHeThongMenu ref={refModalThemHeThongMenu} listMenu={danhSachMenu} />
    </div>
  );
};

export default memo(HeThongMenuContent);

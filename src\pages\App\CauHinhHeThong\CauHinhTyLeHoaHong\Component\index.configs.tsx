import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietDanhMucDaiLyFieldsConfig {
  ma_doi_tac_ql: IFormInput;

  ma: IFormInput;
}
const FormChiTietDanhMucDaiLy: IFormChiTietDanhMucDaiLyFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đ<PERSON>i tác",
    placeholder: "Chọn đối tác",
    rules: [ruleRequired],
  },

  ma: {
    component: "input",
    name: "ma",
    label: "Mã đại lý",
    placeholder: "Mã đại lý",
    rules: [ruleRequired],
  },
};
export const TRANG_THAI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

export default FormChiTietDanhMucDaiLy;
export interface Props {}
export interface IModalChiTietCauHinhTyLeHoaHongRef {
  open: (data?: CommonExecute.Execute.IChiTietDanhMucDaiLy) => void;
  close: () => void;
}
export interface TableTyLeHoaHongDataType {
  key: string;
  ngay_ad: string;
  tlhh: number;
  tlhh_ct: number;
  // hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const tyLeHoaHongColumns: TableProps<TableTyLeHoaHongDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "Ngày áp dụng",
    dataIndex: "ngay_ad",
    key: "ngay_ad",
  },
  {
    ...defaultTableColumnsProps,
    title: "TL hoa hồng (%)",
    dataIndex: "tlhh",
    width: 140,
    key: "tlhh",
  },
  {
    ...defaultTableColumnsProps,
    title: "TL hoa hồng cấp trên (%)",
    dataIndex: "tlhh_ct",
    width: 160,
    key: "tlhh_ct",
  },
  // {
  //   title: "Xóa",
  //   dataIndex: "hanh_dong",
  //   // key: "hanh_dong",
  //   width: 50,

  //   render: (_, record) => record.hanh_dong?.(),
  //   ...defaultTableColumnsProps,
  // },
];
export type DataIndexTyLeHoaHong = keyof TableTyLeHoaHongDataType;
export interface TableCayTyLeHoaHongDataType {
  key: string;
  stt: string;
  ten_dai_ly: string;
  tlhh: number;
  tlhh_ct: number;
  ma_dai_ly: string;
  cap: number;
  // hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const cayTyLeHoaHongColumns: TableProps<TableCayTyLeHoaHongDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: colWidthByKey.sott,
  },
  // {
  //   ...defaultTableColumnsProps,
  //   title: "Mã đại lý",
  //   dataIndex: "ma_dai_ly",
  //   key: "ma_dai_ly",
  //   width: 120,
  // },
  {
    ...defaultTableColumnsProps,
    title: "Tên đại lý",
    dataIndex: "ten_dai_ly",
    key: "ten_dai_ly",
    align: "left",
  },
  {
    ...defaultTableColumnsProps,
    title: "TL hoa hồng (%)",
    dataIndex: "tlhh",
    key: "tlhh",
    width: 140,
  },
  {
    ...defaultTableColumnsProps,
    title: "TL hoa hồng cấp trên (%)",
    dataIndex: "tlhh_ct",
    key: "tlhh_ct",
    width: 160,
  },
];
export type DataIndexCayTyLeHoaHong = keyof TableCayTyLeHoaHongDataType;

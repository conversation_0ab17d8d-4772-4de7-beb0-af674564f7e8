#DANH_MUC_DAI_LY {
  .ant-table-row:hover td {
    background-color: #e8f5e9 !important; //test
  }

  .header-cell-custom {
    background-color: #96bf49 !important;
    // background: linear-gradient(to right, #96bf49, #009a55) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-align: center !important;
    border-radius: 0 !important;
  }

  /* 🔒 Ẩn cả thanh cuộn dọc và ngang */
  .antd-table-hide-scroll .ant-table-body {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
  }

  .antd-table-hide-scroll .ant-table-body::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari */
  }

  //cố định height cho bảng
  // .ant-table-container {
  //   min-height: 620px;
  // }

  //style row đại lý khi được chọn -> highlight background
}
.modal-chon-dai-ly-cha .header-cell-custom {
  background-color: #96bf49 !important;
  color: #fff !important;
  font-weight: bold !important;
  text-align: center !important;
}

.modal-chon-dai-ly-cha .custom-row-selected {
  background-color: #96bf49 !important;
}

.modal-chon-dai-ly-cha .custom-row-selected.ant-table-row:hover td {
  background-color: #e8f5e9 !important;
}
.modal-chon-dai-ly-cha {
}
.modal-chon-dai-ly-cha .ant-table-row:hover td {
  background-color: #e8f5e9 !important;
}
.Dai-ly-cha .ant-table-title {
  padding: 0;
  margin-bottom: 8px;
}

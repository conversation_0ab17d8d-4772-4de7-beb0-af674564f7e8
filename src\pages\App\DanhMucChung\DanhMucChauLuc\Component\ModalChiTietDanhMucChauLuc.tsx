import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiDanhMucChauLuc, TRANG_THAI_TAO_MOI_DANH_MUC_CHAU_LUC} from "../index.configs";
import {useQuanLyDanhMucChauLucContext} from "../index.context";
import {ChiTietDanhMucChauLucProps, IModalChiTietDanhMucChauLucRef} from "./Constant";
const {ma, ten, stt, trang_thai} = FormTaoMoiDanhMucChauLuc;

const ModalChiTietDanhMucChauLucComponent = forwardRef<IModalChiTietDanhMucChauLucRef, ChiTietDanhMucChauLucProps>(({listDanhMucChauLuc}: ChiTietDanhMucChauLucProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucChauLuc?: CommonExecute.Execute.IDanhMucChauLuc) => {
      setIsOpen(true);
      if (dataDanhMucChauLuc) setChiTietDanhMucChauLuc(dataDanhMucChauLuc); // nếu có dữ liệu -> set chi tiết DanhMucChauLuc -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucChauLuc, setChiTietDanhMucChauLuc] = useState<CommonExecute.Execute.IDanhMucChauLuc | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDanhMucChauLuc, getListDanhMucChauLuc, loading} = useQuanLyDanhMucChauLucContext();

  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietDanhMucChauLuc) {
      const arrFormData = [];
      for (const key in chiTietDanhMucChauLuc) {
        arrFormData.push({
          name: key,
          value: chiTietDanhMucChauLuc[key as keyof CommonExecute.Execute.IDanhMucChauLuc],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucChauLuc]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDanhMucChauLuc(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDanhMucChauLucParams = form.getFieldsValue(); //lấy ra values của form
      //với trường hợp tạo mới -> check mã DanhMucChauLuc đã tồn tại
      // if (!chiTietDanhMucChauLuc) {
      //   for (let i = 0; i < listDanhMucChauLuc.length; i++) {
      //     if (listDanhMucChauLuc[i].ma === values.ma) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã DanhMucChauLuc đã tổn tại!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      await capNhatChiTietDanhMucChauLuc(values); //cập nhật lại DanhMucChauLuc
      await getListDanhMucChauLuc(); //lấy lại danh sách DanhMucChauLuc
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span: number = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietDanhMucChauLuc ? true : false}, 12)}
        {renderFormColum({...ten}, 12)}
      </Row>

      <Row gutter={16}>
        {renderFormColum({...stt}, 12)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DANH_MUC_CHAU_LUC}, 12)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietDanhMucChauLuc ? `${chiTietDanhMucChauLuc.ten}` : "Tạo mới danh mục châu lục"} trang_thai_ten={chiTietDanhMucChauLuc?.trang_thai_ten} trang_thai={chiTietDanhMucChauLuc?.trang_thai} />}
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={600}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietDanhMucChauLucComponent.displayName = "ModalChiTietDanhMucChauLucComponent";
export const ModalChiTietDanhMucChauLuc = memo(ModalChiTietDanhMucChauLucComponent, isEqual);

import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {HopDongConNguoiContext} from "./index.context";
import {IHopDongConNguoiContextProps} from "./index.model";
import dayjs from "dayjs";
import {defaultParamsTimKiemPhanTrangHopDong} from "./index.configs";
import {defaultParamsTimKiemPhanTrangNguoiDuocBaoHiem} from "./Component";
import {message} from "antd";
import {FileEndpoint} from "@src/services/axios";

const HopDongConNguoiProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const mutateUseCommonExecuteQuyenLoi = useCommonExecute();
  const [filterHopDongParams, setFilterHopDongParams] = useState<ReactQuery.ILayDanhSachHopDongConNguoiPhanTrangParams>(defaultParamsTimKiemPhanTrangHopDong);
  const [tongSoDongHopDong, setTongSoDongHopDong] = useState<number>(0);

  const [chiTietHopDong, setChiTietHopDong] = useState<CommonExecute.Execute.IHopDongConNguoi | null>(null);

  // DATA BƯỚC 2 : NHÂP ĐỐI TƯỢNG BẢO HIỂM
  const [listNguoiDuocBaoHiem, setListNguoiDuocBaoHiem] = useState<CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi[]>([]);
  const [chiTietNguoiDuocBaoHiem, setChiTietNguoiDuocBaoHiem] = useState<CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi | undefined>();
  const [timKiemPhanTrangNguoiDuocBaoHiemParams, setTimKiemPhanTrangNguoiDuocBaoHiemParams] =
    useState<ReactQuery.ITimKiemPhanTrangNguoiDuocBaoHiemHopDongConNguoi>(defaultParamsTimKiemPhanTrangNguoiDuocBaoHiem);
  const [listGoiBaoHiem, setLisGoiBaoHiem] = useState<CommonExecute.Execute.IGoiBaoHiemConNguoi[]>([]);
  const [tongSoDongNguoiDuocBaoHiem, setTongSoDongNguoiDuocBaoHiem] = useState<number>(0);
  const [tongPhiBaoHiemFromAPI, setTongPhiBaoHiemFromAPI] = useState<number>(0);

  // DATA STEP 4 : THÔNG TIN CẤU HÌNH
  const [listThongTinThanhToanHopDongConNguoi, setlistThongTinThanhToanHopDongConNguoi] = useState<CommonExecute.Execute.IThongTinThanhToanCuaHopDong[]>([]);

  //DATA STEP 5 :
  const [listDongBaoHiemHopDongConNguoi, setListDongBaoHiemHopDongConNguoi] = useState<CommonExecute.Execute.IDongBaoHiem[]>([]);
  const [listDonViDongTai, setListDonViDongTai] = useState<CommonExecute.Execute.IDonViDongTai[]>([]);
  const [listTaiBaoHiemHopDongConNguoi, setListTaiBaoHiemHopDongConNguoi] = useState<CommonExecute.Execute.ITaiBaoHiem[]>([]);

  const [listHopDong, setListHopDong] = useState<CommonExecute.Execute.IHopDongConNguoi[]>([]);
  //DATA CHUNG
  const [listDoiTac, setListDoiTac] = useState<CommonExecute.Execute.IDoiTac[]>([]);
  const [listChiNhanh, setListChiNhanh] = useState<CommonExecute.Execute.IChiNhanh[]>([]);
  const [listSanPham, setListSanPham] = useState<CommonExecute.Execute.ISanPham[]>([]);
  const [listChuongTrinhBaoHiem, setListChuongTrinhBaoHiem] = useState<CommonExecute.Execute.IChuongTrinhBaoHiem[]>([]);
  const [listPhuongThucKhaiThac, setListPhuongThucKhaiThac] = useState<CommonExecute.Execute.IChiTietPhuongThucKhaiThac[]>([]);
  const [listDaiLyKhaiThac, setListDaiLyKhaiThac] = useState<CommonExecute.Execute.IDanhMucDaiLy[]>([]);
  const [listDonViBoiThuongTPA, setListDonViBoiThuongTPA] = useState<CommonExecute.Execute.INhaBaoHiemTPA[]>([]);
  const [listCanBo, setListCanBo] = useState<CommonExecute.Execute.IDoiTacNhanVien[]>([]);
  const [listPhongBan, setListPhongBan] = useState<CommonExecute.Execute.IPhongBan[]>([]);
  const [listNguyenTe, setListNguyenTe] = useState<Array<{ma?: string; ten?: string}>>([]);

  const [windowHeight, setWindowHeight] = useState(window.innerHeight);
  useEffect(() => {
    const onResize = () => setWindowHeight(window.innerHeight); //kích thước màn hình
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, []);

  useEffect(() => {
    initData();
  }, []);

  const initData = () => {
    getListDoiTac();
    getListChiNhanh();
    getListSanPham({nv: "NG", ma_doi_tac_ql: ""});
    getListPhongBan();
    getListDonViBoiThuong({nv: "NG"});
    getListChuongTrinHBaoHiem({nv: "NG", ma_doi_tac_ql: ""});
    getListPhuongThucKhaiThac();
    getListDaiLyKhaiThac();
    getListNguyenTe();
    getListDonViDongTai();
  };

  /* CHỨC DANH */
  // CHI_TIET_HOP_DONG_BAO_HIEM_CON_NGUOI
  const getChiTietHopDong = useCallback(
    async (data: ReactQuery.IChiTietHopDongConNguoiParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          so_id: data.so_id,
          ma_doi_tac_ql: data.ma_doi_tac_ql,
          actionCode: ACTION_CODE.CHI_TIET_HOP_DONG_BAO_HIEM_CON_NGUOI,
        });
        setChiTietHopDong(response.data as CommonExecute.Execute.IHopDongConNguoi);
        return response.data as CommonExecute.Execute.IHopDongConNguoi;
      } catch (error) {
        console.log("getChiTietHopDong err", error);
        return {} as CommonExecute.Execute.IHopDongConNguoi;
      }
    },
    [mutateUseCommonExecute],
  );

  // TÌM KIẾM HỢP ĐỒNG
  const timKiemPhanTrangHopDongBaoHiem = useCallback(async () => {
    try {
      // console.log("timKiemPhanTrangHopDongBaoHiem", filterHopDongParams);
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterHopDongParams,
        tu_ngay: +dayjs(filterHopDongParams.tu_ngay).format("YYYYMMDD"),
        den_ngay: +dayjs(filterHopDongParams.den_ngay).format("YYYYMMDD"),
        gcn: filterHopDongParams.gcn ? filterHopDongParams.gcn + "" : "",
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_HOP_DONG_BAO_HIEM_CON_NGUOI,
      });
      if (response.data) {
        setListHopDong(response.data.data);
        setTongSoDongHopDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("timKiemPhanTrangHopDongBaoHiem error ", error);
    }
  }, [mutateUseCommonExecute, filterHopDongParams]);

  useEffect(() => {
    timKiemPhanTrangHopDongBaoHiem();
  }, [filterHopDongParams]);

  /* KHÁCH HÀNG */
  // TÌM KIẾM PHÂN TRANG KHÁCH HÀNG
  const searchKhachHang = useCallback(
    async (filterKhachHangParams: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...filterKhachHangParams,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_KHACH_HANG,
        });
        return (
          response?.data || {
            data: [],
            tong_so_dong: 0,
          }
        );
      } catch (error) {
        console.log("searchKhachHang error ", error);
        return {
          data: [],
          tong_so_dong: 0,
        };
      }
    },
    [mutateUseCommonExecute],
  );

  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  /* CHI NHÁNH */
  const getListChiNhanh = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_CHI_NHANH,
      });
      setListChiNhanh(response?.data.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListChiNhanh error ", error);
    }
  }, [mutateUseCommonExecute]);

  /* PHÒNG BAN */
  const getListPhongBan = useCallback(
    async (params?: ReactQuery.ILayDanhSachPhongBanPhanTrangParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_DANH_SACH_PHONG_BAN,
        });
        setListPhongBan(response.data);
      } catch (error) {
        console.log("getListPhongBan error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  /** CÁN BỘ QUẢN LÝ */
  const getListCanBoQuanLy = useCallback(
    async (params?: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_CAN_BO_QUAN_LY,
        });
        setListCanBo(response?.data.data);
        return {data: response.data.data, tong_so_dong: response.data.tong_so_dong};
      } catch (error) {
        console.log("getListCanBoQuanLy error ", error);
        return {data: [], tong_so_dong: 0};
      }
    },
    [mutateUseCommonExecute],
  );

  // SẢN PHẨM
  const getListSanPham = useCallback(
    async (params?: ReactQuery.ILietKeSanPhamParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_SAN_PHAM,
        });
        setListSanPham(response.data);
      } catch (error) {
        console.log("getListSanPham error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  // CHUONG TRÌNH BẢO HIỂM
  const getListChuongTrinHBaoHiem = useCallback(
    async (params?: ReactQuery.ILietKeChuongTrinhBaoHiemParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_CHUONG_TRINH_BAO_HIEM,
        });
        setListChuongTrinhBaoHiem(response.data);
      } catch (error) {
        console.log("getListChuongTrinHBaoHiem error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  // PHƯƠNG THỨC KHAI THÁC
  const getListPhuongThucKhaiThac = useCallback(
    async (params?: ReactQuery.ILietKePhuongThucKhaiThacParams) => {
      try {
        const response: {data: Array<CommonExecute.Execute.IChiTietPhuongThucKhaiThac>} = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LAY_DS_PHUONG_THUC_KHAI_THAC,
        });
        setListPhuongThucKhaiThac(response.data);
      } catch (error) {
        console.log("getListPhuongThucKhaiThac error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  // LIỆT KÊ ĐẠI LÝ KHAI THÁC
  const getListDaiLyKhaiThac = useCallback(
    async (params?: ReactQuery.ILietKeDanhSachDaiLyParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DAI_LY,
        });
        setListDaiLyKhaiThac(response.data);
      } catch (error) {
        console.log("getListDaiLyKhaiThac error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  // TÌM KIẾM ĐẠI LÝ
  const searchDaiLy = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_DAI_LY,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);
        return {data: response.data.data, tong_so_dong: response.data.tong_so_dong};
      } catch (error) {
        console.log("timKiemPhanTrangDaiLy", error);
        return {data: [], tong_so_dong: 0};
      }
    },
    [mutateUseCommonExecute],
  );

  // ĐƠN VỊ BỒI THƯỜNG
  const getListDonViBoiThuong = useCallback(
    async (params?: ReactQuery.ILietKeDonViBoiThuongTPAParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          nv: "NG",
          actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DON_VI_BOI_THUONG_TPA,
        });
        setListDonViBoiThuongTPA(response.data);
      } catch (error) {
        console.log("getListDonViBoiThuong error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  // CẬP NHẬT HỢP ĐỒNG
  const updateHopDong = useCallback(
    async (params: ReactQuery.IUpdateHopDongParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LUU_THONG_TIN_HOP_DONG_CON_NGUOI,
        });
        return response;
      } catch (error) {
        console.log("updateHopDong error ", error);
        return {
          data: 0,
          output: {
            so_hd: "",
            so_id: 0,
          },
        };
      }
    },
    [mutateUseCommonExecute],
  );

  // TÌM KIẾM PHÂN TRÁNG NGƯỜI ĐƯỢC BẢO HIỂM
  const timKiemPhanTrangNguoiDuocBaoHiem = useCallback(async () => {
    // Kiểm tra so_id hợp lệ trước khi gọi API
    if (!timKiemPhanTrangNguoiDuocBaoHiemParams.so_id || Number(timKiemPhanTrangNguoiDuocBaoHiemParams.so_id) <= 0) {
      message.error("Không có thông tin hợp đồng.");
      return;
    }

    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...timKiemPhanTrangNguoiDuocBaoHiemParams,
        gcn: "",
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_NGUOI_DUOC_BAO_HIEM_HOP_DONG_CON_NGUOI,
      });
      if (response.data?.data) {
        setListNguoiDuocBaoHiem(response.data.data);
        setTongSoDongNguoiDuocBaoHiem(response.data.tong_so_dong);
        // Cập nhật tổng phí bảo hiểm từ API
        const tongPhiFromAPI = (response as any).output?.tong_phi;
        setTongPhiBaoHiemFromAPI(Number(tongPhiFromAPI));
        getChiTietNguoiDuocBaoHiem(
          !chiTietNguoiDuocBaoHiem
            ? response.data.data[0]
            : {
                so_id: chiTietNguoiDuocBaoHiem.so_id,
                so_id_dt: chiTietNguoiDuocBaoHiem.so_id_dt,
              },
        );
      }
    } catch (error) {
      console.log("timKiemPhanTrangNguoiDuocBaoHiem error ", error);
    }
  }, [mutateUseCommonExecute, timKiemPhanTrangNguoiDuocBaoHiemParams, chiTietNguoiDuocBaoHiem]);

  useEffect(() => {
    console.log("timKiemPhanTrangNguoiDuocBaoHiemParams", timKiemPhanTrangNguoiDuocBaoHiemParams);

    // Chỉ gọi API khi có so_id hợp lệ (đã chọn hợp đồng)
    if (timKiemPhanTrangNguoiDuocBaoHiemParams.so_id && Number(timKiemPhanTrangNguoiDuocBaoHiemParams.so_id) > 0) {
      timKiemPhanTrangNguoiDuocBaoHiem();
    }
  }, [timKiemPhanTrangNguoiDuocBaoHiemParams]);

  //GET CHI TIẾT NGƯỜI ĐƯỢC BẢO HIỂM
  const getChiTietNguoiDuocBaoHiem = useCallback(
    async (params: ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          so_id_dt: +(params?.so_id_dt || 0),
          actionCode: ACTION_CODE.CHI_TIET_NGUOI_DUOC_BAO_HIEM_HOP_DONG_CON_NGUOI,
        });
        console.log("getChiTietNguoiDuocBaoHiem response", response);
        if (response.data) setChiTietNguoiDuocBaoHiem(response?.data);
        return response;
      } catch (error) {
        console.log("chiTietNguoiDuocBaoHiem error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  // LƯU THÔNG TIN NGƯỜI ĐƯỢC BẢO HIỂM
  const luuThongTinNguoiDuocBaoHiem = useCallback(
    async (params: ReactQuery.ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          so_id_dt: +(params.so_id_dt || 0),
          gcn: params.gcn + "",
          actionCode: ACTION_CODE.LUU_THONG_TIN_NGUOI_DUOC_BAO_HIEM_HOP_DONG_CON_NGUOI,
        });
        console.log("luuThongTinNguoiDuocBaoHiem response", response);
        return response;
      } catch (error) {
        console.log("chiTietNguoiDuocBaoHiem error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  // TÌM KIẾM PHÂN TRANG GÓI BẢO HIỂM
  const timKiemPhanTrangGoiBaoHiem = useCallback(
    async (params: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          trang: 1,
          so_dong: 1000,
          ngay_ad: undefined,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_GOI_BAO_HIEM,
        });
        setLisGoiBaoHiem(response?.data?.data);
      } catch (error) {
        console.log("timKiemPhanTrangGoiBaoHiem error ", error);
      }
    },
    [mutateUseCommonExecute],
  );

  // LIỆT KÊ ĐIỀU KHOẢN NGƯỜI ĐƯỢC BẢO HIỂM
  const lietKeDieuKhoanNguoiDuocBaoHiem = useCallback(
    async (params: ReactQuery.ILietKeDieuKhoanNguoiDuocBaoHiemParams) => {
      try {
        console.log("lietKeDieuKhoanNguoiDuocBaoHiem");
        const response = await mutateUseCommonExecuteQuyenLoi.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_DIEU_KHOAN_NGUOI_DUOC_BAO_HIEM,
        });
        console.log("lietKeDieuKhoanNguoiDuocBaoHiem response", response);
        if (response?.data) {
          // setListQuyenLoiNguoiDuocBaoHiem(response.data);
          return response?.data;
        }
      } catch (error) {
        console.log("lietKeDieuKhoanNguoiDuocBaoHiem error ", error);
      }
    },
    [mutateUseCommonExecuteQuyenLoi],
  );

  // LƯU ĐIỀU KHOẢN NGƯỜI ĐƯỢC BẢO HIỂM
  const luuDieuKhoanNguoiDuocBaoHiem = useCallback(
    async (params: ReactQuery.ILuuDieuKhoanNguoiDuocBaoHiemParams) => {
      try {
        console.log("luuDieuKhoanNguoiDuocBaoHiem");
        const response = await mutateUseCommonExecuteQuyenLoi.mutateAsync({
          ...params,
          so_id_dt: +(params.so_id_dt || 0),
          actionCode: ACTION_CODE.LUU_DIEU_KHOAN_NGUOI_DUOC_BAO_HIEM,
        });
        console.log("luuDieuKhoanNguoiDuocBaoHiem response", response);
        if (response?.data) return response?.data;
      } catch (error) {
        console.log("luuDieuKhoanNguoiDuocBaoHiem error ", error);
      }
    },
    [mutateUseCommonExecuteQuyenLoi],
  );

  // LIỆT KÊ ĐIỀU KHOẢN BỔ SUNG NGƯỜI ĐƯỢC BẢO HIỂM
  const lietKeDieuKhoanBoSungNguoiDuocBaoHiem = useCallback(
    async (params: ReactQuery.ILuuDieuKhoanBoSungNguoiDuocBaoHiemParams) => {
      try {
        console.log("lietKeDieuKhoanBoSungNguoiDuocBaoHiem");
        const response = await mutateUseCommonExecuteQuyenLoi.mutateAsync({
          ...params,
          so_id_dt: +(params.so_id_dt || 0),
          actionCode: ACTION_CODE.LIET_KE_DIEU_KHOAN_BO_SUNG_NGUOI_DUOC_BAO_HIEM,
        });
        console.log("lietKeDieuKhoanBoSungNguoiDuocBaoHiem response", response);
        if (response?.data) return response?.data;
      } catch (error) {
        console.log("lietKeDieuKhoanBoSungNguoiDuocBaoHiem error ", error);
      }
    },
    [mutateUseCommonExecuteQuyenLoi],
  );
  // LƯU ĐIỀU KHOẢN NGƯỜI ĐƯỢC BẢO HIỂM
  const luuDieuKhoanBoSungNguoiDuocBaoHiem = useCallback(
    async (params: ReactQuery.ILuuDieuKhoanBoSungNguoiDuocBaoHiemParams) => {
      try {
        console.log("luuDieuKhoanBoSungNguoiDuocBaoHiem");
        const response = await mutateUseCommonExecuteQuyenLoi.mutateAsync({
          ...params,
          so_id_dt: +(params.so_id_dt || 0),
          actionCode: ACTION_CODE.LUU_DIEU_KHOAN_BO_SUNG_NGUOI_DUOC_BAO_HIEM,
        });
        console.log("luuDieuKhoanBoSungNguoiDuocBaoHiem response", response);
        if (response?.data) return response?.data;
      } catch (error) {
        console.log("luuDieuKhoanBoSungNguoiDuocBaoHiem error ", error);
      }
    },
    [mutateUseCommonExecuteQuyenLoi],
  );

  /* NGUYÊN TỆ */
  const getListNguyenTe = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_BO_MA_NGUYEN_TE,
      });
      if (response.data) setListNguyenTe(response?.data.map(item => ({ma: item.ma, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListNguyenTe error ", error);
    }
  }, [mutateUseCommonExecute]);

  /* GET LIST NGƯỜI PHỤ THUỘC */
  const getListNguoiPhuThuoc = useCallback(
    async (params: ReactQuery.ILietKeDanhSachNguoiPhuThuocHopDongConNguoiParams) => {
      try {
        console.log("getListNguoiPhuThuoc", params);
        const response = await mutateUseCommonExecuteQuyenLoi.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_DANH_SACH_NGUOI_PHU_THUOC_HOP_DONG_CON_NGUOI,
        });
        console.log("getListNguoiPhuThuoc", response.data);
        return response.data;
      } catch (error) {
        console.log("getListNguoiPhuThuoc error ", error);
      }
    },
    [mutateUseCommonExecuteQuyenLoi],
  );

  /* LẤY CHI TIẾT NGƯỜI PHỤ THUỘC */
  const getChiTietNguoiPhuThuoc = useCallback(
    async (params: ReactQuery.IChiTietNguoiPhuThuocHopDongConNguoiParams) => {
      try {
        const response = await mutateUseCommonExecuteQuyenLoi.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.CHI_TIET_NGUOI_PHU_THUOC_HOP_DONG_CON_NGUOI,
        });
        console.log("chiTietNguoiPhuThuoc response", response.data);
        return response.data;
      } catch (error) {
        console.log("chiTietNguoiPhuThuoc error ", error);
      }
    },
    [mutateUseCommonExecuteQuyenLoi],
  );

  /* CẬP NHẬT NGƯỜI PHỤ THUỘC */
  const capNhatNguoiPhuThuoc = useCallback(
    async (params: ReactQuery.ICapNhatNguoiPhuThuocHopDongConNguoiParams) => {
      try {
        const response = await mutateUseCommonExecuteQuyenLoi.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LUU_THONG_TIN_NGUOI_PHU_THUOC_HOP_DONG_CON_NGUOI,
        });
        console.log("capNhatNguoiPhuThuoc", response);
        return response;
      } catch (error) {
        console.log("capNhatNguoiPhuThuoc error ", error);
      }
    },
    [mutateUseCommonExecuteQuyenLoi],
  );

  /* GET DANH SÁCH FILE THUMBNAIL THEO ĐÔI TƯỢNG */
  const getDanhSachFileThumbnailTheoDoiTuong = useCallback(
    async (params: ReactQuery.IGetFileThumbnailParams) => {
      console.log("getDanhSachFileThumbnailTheoDoiTuong");
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.GET_FILE_THUMBNAIL,
        });
        console.log("getDanhSachFileThumbnailTheoDoiTuong response", response);
        return response;
      } catch (error: any) {
        console.log("getDanhSachFileThumbnailTheoDoiTuong error ", error.message | error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //Upload file theo đối tượng
  const uploadFileTheoDoiTuong = useCallback(
    async (params: ReactQuery.IUploadFileTheoDoiTuongXeParams): Promise<boolean> => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.UPLOAD_FILE_THEO_DOI_TUONG,
        });
        console.log("uploadFileTheoDoiTuong response", response);
        if (response?.data === -1) {
          message.success("Upload file thành công!");
          return true;
        } else {
          message.error("Upload file thất bại!");
          return false;
        }
      } catch (error: any) {
        console.log("uploadFileTheoDoiTuong error ", error.message | error);
        message.error("Upload file thất bại!");
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  //Xoá file theo đối tượng
  const deleteFileTheoDoiTuong = useCallback(
    async (params: ReactQuery.IUploadFileTheoDoiTuongXeParams): Promise<boolean> => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.XOA_FILE_THEO_DOI_TUONG, // đúng constant
        });
        console.log("deleteFileTheoDoiTuong response", response);
        if (response.data === -1) {
          message.success("Xoá file thành công!");
          return true;
        } else {
          message.error("Xoá file thất bại!");
          return false;
        }
      } catch (error: any) {
        console.log("deleteFileTheoDoiTuong error ", error.message | error);
        message.error("Xoá file thất bại!");
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  //LK DS MỨC ĐỘ TỔN THẤT CON NGƯỜI
  const getListMucDoTonThatConNguoi = useCallback(async (): Promise<any> => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_MUC_DO_TON_THAT_XE,
      });
      // setListMucDoTonThatXe(responseData.data);
      return response.data;
    } catch (error: any) {
      console.log("getListMucDoTonThatConNguoi error ", error.message || error);
      return null;
    }
  }, [mutateUseCommonExecute]);

  //Lấy thông tin thanh toán của hợp đồng bảo hiểm con người
  const layThongTinThanhToanCuaHopDongBaoHiemConNguoi = useCallback(
    async (params: ReactQuery.ILayThongTinThanhToanCuaHopDongBaoHiemParams): Promise<any> => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          so_id: params.so_id || 0,
          actionCode: ACTION_CODE.LIET_KE_THONG_TIN_THANH_TOAN_CUA_HOP_DONG_BAO_HIEM,
        });
        setlistThongTinThanhToanHopDongConNguoi(response.data);
        return response.data;
      } catch (error: any) {
        console.log("layThongTinThanhToanCuaHopDongBaoHiemConNguoi error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //update kỳ thanh toán
  const updateKyThanhToan = useCallback(
    async (params: ReactQuery.IUpdateKyThanhToanParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.CAP_NHAT_TTIN_KY_TTOAN_CUA_HOP_DONG_BAO_HIEM,
        });
        if ((response.data as unknown as number) === -1) {
          message.success("Lưu thông tin kỳ thanh toán thành công!");
          layThongTinThanhToanCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id});
          return true;
        }
        return false;
      } catch (error: any) {
        console.log("updateKyThanhToan error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  //get chi tiết kỳ thanh toán
  const layChiTietKyThanhToan = useCallback(
    async (params: ReactQuery.IChiTietKyThanhToanParams) => {
      try {
        const responseData = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.XEM_CHI_TIET_TTIN_KY_TTOAN_CUA_HOP_DONG_BAO_HIEM,
        });
        return responseData.data;
      } catch (error: any) {
        console.log("layChiTietKyThanhToan error ", error.message | error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  // LIỆT KÊ DANH SÁCH CÁC ĐỐI TƯỢNG ĐÃ ĐƯỢC ÁP DỤNG ĐỒNG BẢO HIỂM
  const lietKeDanhSachCacDoiTuongDaDuocApDungDongBH = useCallback(
    async (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams): Promise<any> => {
      try {
        const bodyParams = {
          so_id: chiTietHopDong?.so_id || 0,
          ma_dvi_dong: params.ma_dvi_dong,
          actionCode: ACTION_CODE.LIET_KE_DS_CAC_DOI_TUONG_DA_DUOC_AD_DONG_BAO_HIEM,
        };
        const response = await mutateUseCommonExecute.mutateAsync(bodyParams);
        return response.data;
      } catch (error: any) {
        console.log("lietKeDanhSachCacDoiTuongDaDuocApDungDongBH error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute, chiTietHopDong],
  );

  //Lấy thông tin đồng của hợp đồng con người
  const layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi = useCallback(
    async (body: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams): Promise<any> => {
      try {
        const params = {
          so_id: body.so_id || 0,
          actionCode: ACTION_CODE.LIET_KE_DS_CAU_HINH_DONG_CUA_HOP_DONG_BAO_HIEM,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);
        setListDongBaoHiemHopDongConNguoi(response.data);
        return response.data;
      } catch (error: any) {
        console.log("layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //get chi tiết thông tin đồng BH
  const layChiTietThongTinCauHinhDongBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => {
      try {
        const params = {
          so_id: chiTietHopDong?.so_id,
          ma_dvi_dong: body.ma_dvi_dong,
          actionCode: ACTION_CODE.XEM_CHI_TIET_TTIN_CAU_HINH_DONG_BH,
        };
        const responseData = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data as CommonExecute.Execute.IDongBaoHiem;
      } catch (error: any) {
        console.log("layChiTietThongTinCauHinhDongBH error ", error.message | error);
        return null;
      }
    },
    [mutateUseCommonExecute, chiTietHopDong],
  );

  //Xoá thông tin đồng BH
  const xoaThongTinDongBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => {
      try {
        const params = {
          so_id: chiTietHopDong?.so_id,
          ma_dvi_dong: body.ma_dvi_dong,
          actionCode: ACTION_CODE.XOA_TTIN_CAU_HINH_DONG_BH,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Xoá thông tin đồng BH thành công!");
          layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("xoaThongTinDongBH error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDong, layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi],
  );

  //Lấy danh sách đơn vị đồng tái
  const getListDonViDongTai = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_MA_DV_DONG_TAI,
      });
      setListDonViDongTai((response?.data as unknown as Array<CommonExecute.Execute.IDonViDongTai>) || []);
    } catch (error) {
      console.log("getListDonViDongTai error ", error);
      return {data: []};
    }
  }, [mutateUseCommonExecute]);

  //Lưu/cập nhật cấu hình đồng bảo hiểm con người
  const updateCauHinhDongBaoHiem = useCallback(
    async (body: ReactQuery.IUpdateCauHinhDongBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HINH_DONG_CUA_HOP_DONG_BAO_HIEM,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Lưu thông tin cấu hình đồng bảo hiểm thành công!");
          layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateCauHinhDongBaoHiem error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDong],
  );

  //Lấy thông tin đồng của hợp đồng con người
  const layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi = useCallback(
    async (body: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams): Promise<any> => {
      try {
        const params = {
          so_id: body.so_id || 0,
          actionCode: ACTION_CODE.LIET_KE_DS_CAU_HINH_TAI_BH_CUA_HOP_DONG_BAO_HIEM,
        };
        const responseData = await mutateUseCommonExecute.mutateAsync(params);
        setListTaiBaoHiemHopDongConNguoi(responseData.data);
        return responseData.data;
      } catch (error: any) {
        console.log("layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //get chi tiết thông tin đồng BH
  const layChiTietThongTinCauHinhTaiBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => {
      try {
        const params = {
          so_id: chiTietHopDong?.so_id,
          ma_dvi_tai: body.ma_dvi_tai,
          actionCode: ACTION_CODE.XEM_CHI_TIET_TTIN_CAU_HINH_TAI_BH,
        };
        const responseData = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data as CommonExecute.Execute.ITaiBaoHiem;
      } catch (error: any) {
        console.log("layChiTietThongTinCauHinhTaiBH error ", error.message | error);
        return null;
      }
    },
    [mutateUseCommonExecute, chiTietHopDong],
  );

  //Lưu/cập nhật cấu hình tái bảo hiểm con người
  const updateCauHinhTaiBaoHiem = useCallback(
    async (body: ReactQuery.IUpdateCauHinhTaiBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HINH_TAI_BH_CUA_HOP_DONG_BAO_HIEM,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Lưu thông tin cấu hình tái bảo hiểm thành công!");
          layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateCauHinhTaiBaoHiem error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDong, layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi],
  );

  //Xoá thông tin tái BH
  const xoaThongTinTaiBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => {
      try {
        const params = {
          so_id: chiTietHopDong?.so_id,
          ma_dvi_tai: body.ma_dvi_tai,
          actionCode: ACTION_CODE.XOA_TTIN_CAU_HINH_TAI_BH,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Xoá thông tin tái BH thành công!");
          layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("xoaThongTinTaiBH error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDong],
  );

  //update đối tượng áp dụng tl đồng BH
  const updateDoiTuongApDungTyLeDongBH = useCallback(
    async (body: ReactQuery.IUpdateDoiTuongApDungDongBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_DOI_TUONG_AP_DUNG_TL_DONG_BH,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin đối tượng áp dụng thành công!");
          layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateDoiTuongApDungTyLeDongBH error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  //Cập nhật đối tượng áp dụng tái BH
  const updateDoiTuongApDungTaiBH = useCallback(
    async (body: ReactQuery.IUpdateDoiTuongApDungTaiBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_DOI_TUONG_AP_DUNG_TAI_BH,
        };
        const response = await mutateUseCommonExecute.mutateAsync(params as any);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin đối tượng áp dụng thành công!");
          layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi({so_id: chiTietHopDong?.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateDoiTuongApDungTaiBH error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDong, layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi],
  );

  const lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhTaiBHParams): Promise<any> => {
      try {
        const params = {
          so_id: chiTietHopDong?.so_id || 0,
          ma_dvi_tai: body.ma_dvi_tai,
          actionCode: ACTION_CODE.LIET_KE_DS_CAC_DOI_TUONG_DA_DUOC_AD_TAI_BAO_HIEM,
        };
        const responseData = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data;
      } catch (error: any) {
        console.log("lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  // Export PDF hợp đồng
  const exportPdfHopDong = useCallback(async (params: ReactQuery.IExportPDFHopDongParams): Promise<string | null> => {
    const actionCode = params.so_id_dt ? ACTION_CODE.EXPORT_PDF_GCN : ACTION_CODE.EXPORT_PDF_HOP_DONG;
    try {
      const requestParams = {
        ...params,
        actionCode,
      };
      const blob = await FileEndpoint.exportPdfHopDong(requestParams);

      // Kiểm tra blob có hợp lệ không
      if (!blob || blob.size === 0) {
        throw new Error("PDF blob is empty or invalid");
      }

      // Kiểm tra kích thước blob có hợp lý không (PDF thường > 1KB)
      if (blob.size < 1000) {
        console.warn("⚠️ Blob size is very small:", blob.size, "bytes");

        // Thử đọc nội dung blob để debug
        try {
          const text = await blob.text();
          console.log("🔍 Blob content (first 500 chars):", text.substring(0, 500));

          // Nếu blob chứa JSON error message
          if (text.startsWith("{") || text.startsWith("[")) {
            const errorData = JSON.parse(text);
            message.error(`Lỗi từ server: ${errorData.message || errorData.error_message || "Unknown error"}`);
            return null;
          }
        } catch (parseError) {
          console.log("🔍 Could not parse blob as text/JSON");
        }

        throw new Error(`PDF file too small (${blob.size} bytes). This might be an error response.`);
      }

      // Kiểm tra blob có phải là PDF không
      if (blob.type && !blob.type.includes("pdf") && !blob.type.includes("octet-stream")) {
        console.warn("⚠️ Blob type is not PDF:", blob.type);
      }

      // Tạo URL từ blob để hiển thị PDF
      const pdfUrl = URL.createObjectURL(blob);
      return pdfUrl;
    } catch (error) {
      console.error("❌ exportPdfHopDong error:", error);
      message.error("Không thể tải file PDF!");
      return null;
    }
  }, []);

  // API lấy danh sách câu hỏi đánh giá sức khoẻ
  const layDanhSachCauHoiDanhGiaSucKhoe = useCallback(
    async (params: ReactQuery.ILietKeCauHoiDanhGiaSucKhoeConNguoiParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          actionCode: ACTION_CODE.LIET_KE_CAU_HOI_DANH_GIA_SUC_KHOE_CON_NGUOI,
          so_id: params.so_id,
          so_id_dt: params.so_id_dt,
        });

        if (response?.data) {
          return {
            cau_hoi: (response.data as any).cau_hoi || [],
            gia_tri: (response.data as any).gia_tri || [],
          };
        }

        return {cau_hoi: [], gia_tri: []};
      } catch (error) {
        console.error("❌ layDanhSachCauHoiDanhGiaSucKhoe error:", error);
        message.error("Không thể tải danh sách câu hỏi đánh giá sức khoẻ!");
        return {cau_hoi: [], gia_tri: []};
      }
    },
    [mutateUseCommonExecute],
  );

  // API lưu đánh giá sức khoẻ
  const luuDanhGiaSucKhoe = useCallback(
    async (params: ReactQuery.ILuuDanhGiaSucKhoeConNguoiParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          actionCode: ACTION_CODE.LUU_DANH_GIA_SUC_KHOE_CON_NGUOI,
          so_id: params.so_id,
          so_id_dt: params.so_id_dt,
          dgsk: params.dgsk,
        });

        if (response?.data) {
          message.success("Lưu đánh giá sức khoẻ thành công!");
          return true;
        }

        message.error("Lưu đánh giá sức khoẻ thất bại!");
        return false;
      } catch (error) {
        console.error("❌ luuDanhGiaSucKhoe error:", error);
        message.error("Không thể lưu đánh giá sức khoẻ!");
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  // API tìm kiếm bệnh viện phân trang cho hợp đồng con người
  const timKiemBenhVienPhanTrangHopDongConNguoi = useCallback(
    async (params: ReactQuery.ITimKiemBenhVienHopDongConNguoiParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.TIM_KIEM_BENH_VIEN_PHAN_TRANG_HOP_DONG_CON_NGUOI,
        });

        return response?.data || {};
      } catch (error) {
        console.log("timKiemBenhVienPhanTrangHopDongConNguoi error:", error);
        return {};
      }
    },
    [mutateUseCommonExecute],
  );

  // API lấy danh sách bệnh viện đã lưu cho hợp đồng con người
  const layDanhSachBenhVienDaLuuHopDongConNguoi = useCallback(
    async (params: ReactQuery.ILayDanhSachBenhVienDaLuuHopDongConNguoiParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LAY_DANH_SACH_BENH_VIEN_DA_LUU_HOP_DONG_CON_NGUOI,
        });

        return response?.data || {};
      } catch (error) {
        console.log("layDanhSachBenhVienDaLuuHopDongConNguoi error:", error);
        return {};
      }
    },
    [mutateUseCommonExecute],
  );

  // API lưu cấu hình bệnh viện cho hợp đồng con người
  const luuCauHinhBenhVienHopDongConNguoi = useCallback(
    async (params: ReactQuery.ILuuCauHinhBenhVienHopDongConNguoiParams) => {
      try {
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LUU_CAU_HINH_BENH_VIEN_HOP_DONG_CON_NGUOI,
        });

        if ((response.data as unknown as number) === -1) {
          message.success("Lưu cấu hình bệnh viện thành công!");
          return true;
        } else {
          return false;
        }
      } catch (error: any) {
        console.log("luuCauHinhBenhVienHopDongConNguoi error:", error.message || error);
        message.error("Có lỗi xảy ra khi lưu cấu hình bệnh viện!");
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  const value = useMemo<IHopDongConNguoiContextProps>(
    () => ({
      // DATA CHUNG
      listHopDong,
      listDoiTac,
      listChiNhanh,
      listSanPham,
      listChuongTrinhBaoHiem,
      listPhuongThucKhaiThac,
      listDaiLyKhaiThac,
      listDonViBoiThuongTPA,
      listCanBo,
      listPhongBan,
      listNguyenTe,
      windowHeight,
      chiTietHopDong,

      //DATA SCREEN DANH SÁCH HỢP ĐỒNG
      tongSoDongHopDong,
      loading: mutateUseCommonExecute.isLoading,
      filterHopDongParams,

      // DATA STEP 2 NHẬP ĐỐI TƯỢNG BẢO HIỂM
      timKiemPhanTrangNguoiDuocBaoHiemParams,
      listNguoiDuocBaoHiem,
      tongSoDongNguoiDuocBaoHiem,
      tongPhiBaoHiemFromAPI,
      listGoiBaoHiem,
      chiTietNguoiDuocBaoHiem,
      loadingQuyenLoi: mutateUseCommonExecuteQuyenLoi.isLoading,

      //DATA STEP 4 : THÔNG TIN CẤU HÌNH
      listThongTinThanhToanHopDongConNguoi,
      listDongBaoHiemHopDongConNguoi,
      listDonViDongTai,
      listTaiBaoHiemHopDongConNguoi,

      timKiemPhanTrangHopDongBaoHiem,
      getChiTietHopDong,
      setFilterHopDongParams,
      searchKhachHang,
      searchDaiLy,
      getListDoiTac,
      getListChiNhanh,
      getListPhongBan,
      getListCanBoQuanLy,
      getListSanPham,
      getListChuongTrinHBaoHiem,
      getListPhuongThucKhaiThac,
      getListDaiLyKhaiThac,
      getListDonViBoiThuong,
      updateHopDong,
      timKiemPhanTrangNguoiDuocBaoHiem,
      getChiTietNguoiDuocBaoHiem,
      luuThongTinNguoiDuocBaoHiem,
      setTimKiemPhanTrangNguoiDuocBaoHiemParams,
      timKiemPhanTrangGoiBaoHiem,
      setChiTietNguoiDuocBaoHiem,
      lietKeDieuKhoanNguoiDuocBaoHiem,
      luuDieuKhoanNguoiDuocBaoHiem,
      lietKeDieuKhoanBoSungNguoiDuocBaoHiem,
      luuDieuKhoanBoSungNguoiDuocBaoHiem,
      getListNguoiPhuThuoc,
      getChiTietNguoiPhuThuoc,
      capNhatNguoiPhuThuoc,
      getDanhSachFileThumbnailTheoDoiTuong,
      uploadFileTheoDoiTuong,
      deleteFileTheoDoiTuong,
      layThongTinThanhToanCuaHopDongBaoHiemConNguoi,
      updateKyThanhToan,
      layChiTietKyThanhToan,
      layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi,
      layChiTietThongTinCauHinhDongBH,
      xoaThongTinDongBH,
      lietKeDanhSachCacDoiTuongDaDuocApDungDongBH,
      updateCauHinhDongBaoHiem,
      layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi,
      layChiTietThongTinCauHinhTaiBH,
      updateCauHinhTaiBaoHiem,
      xoaThongTinTaiBH,
      updateDoiTuongApDungTaiBH,
      lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH,
      exportPdfHopDong,
      updateDoiTuongApDungTyLeDongBH,
      layDanhSachCauHoiDanhGiaSucKhoe,
      luuDanhGiaSucKhoe,

      // Bệnh viện API functions cho Hợp đồng con người
      timKiemBenhVienPhanTrangHopDongConNguoi,
      layDanhSachBenhVienDaLuuHopDongConNguoi,
      luuCauHinhBenhVienHopDongConNguoi,
    }),
    [
      mutateUseCommonExecute,
      mutateUseCommonExecuteQuyenLoi,
      // DATA CHUNG
      listHopDong,
      listDoiTac,
      listChiNhanh,
      listSanPham,
      listChuongTrinhBaoHiem,
      listPhuongThucKhaiThac,
      listDaiLyKhaiThac,
      listDonViBoiThuongTPA,
      listCanBo,
      listPhongBan,
      listNguyenTe,
      windowHeight,
      chiTietHopDong,

      //DATA SCREEN DANH SÁCH HỢP ĐỒNG
      tongSoDongHopDong,
      filterHopDongParams,

      // DATA STEP 2 NHẬP ĐỐI TƯỢNG BẢO HIỂM
      timKiemPhanTrangNguoiDuocBaoHiemParams,
      listNguoiDuocBaoHiem,
      tongSoDongNguoiDuocBaoHiem,
      tongPhiBaoHiemFromAPI,
      listGoiBaoHiem,
      chiTietNguoiDuocBaoHiem,

      //DATA STEP 4 : THÔNG TIN CẤU HÌNH
      listThongTinThanhToanHopDongConNguoi,
      listDongBaoHiemHopDongConNguoi,
      listDonViDongTai,
      listTaiBaoHiemHopDongConNguoi,

      timKiemPhanTrangHopDongBaoHiem,
      getChiTietHopDong,
      searchKhachHang,
      searchDaiLy,
      getListDoiTac,
      getListChiNhanh,
      getListPhongBan,
      getListCanBoQuanLy,
      getListSanPham,
      getListChuongTrinHBaoHiem,
      getListPhuongThucKhaiThac,
      getListDaiLyKhaiThac,
      getListDonViBoiThuong,
      updateHopDong,
      timKiemPhanTrangNguoiDuocBaoHiem,
      getChiTietNguoiDuocBaoHiem,
      luuThongTinNguoiDuocBaoHiem,
      timKiemPhanTrangGoiBaoHiem,
      setChiTietNguoiDuocBaoHiem,
      lietKeDieuKhoanNguoiDuocBaoHiem,
      luuDieuKhoanNguoiDuocBaoHiem,
      lietKeDieuKhoanBoSungNguoiDuocBaoHiem,
      luuDieuKhoanBoSungNguoiDuocBaoHiem,
      getListNguoiPhuThuoc,
      getChiTietNguoiPhuThuoc,
      capNhatNguoiPhuThuoc,
      getDanhSachFileThumbnailTheoDoiTuong,
      uploadFileTheoDoiTuong,
      deleteFileTheoDoiTuong,
      layThongTinThanhToanCuaHopDongBaoHiemConNguoi,
      updateKyThanhToan,
      layChiTietKyThanhToan,
      layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi,
      layChiTietThongTinCauHinhDongBH,
      xoaThongTinDongBH,
      lietKeDanhSachCacDoiTuongDaDuocApDungDongBH,
      updateCauHinhDongBaoHiem,
      layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi,
      layChiTietThongTinCauHinhTaiBH,
      xoaThongTinTaiBH,
      lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH,
      exportPdfHopDong,
      layDanhSachCauHoiDanhGiaSucKhoe,
      luuDanhGiaSucKhoe,
      timKiemBenhVienPhanTrangHopDongConNguoi,
      layDanhSachBenhVienDaLuuHopDongConNguoi,
      luuCauHinhBenhVienHopDongConNguoi,
    ],
  );

  return <HopDongConNguoiContext.Provider value={value}>{children}</HopDongConNguoiContext.Provider>;
};

export default HopDongConNguoiProvider;

import {CheckOutlined, PlusCircleOutlined, SettingOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {formatDateTimeToNumber} from "@src/utils";
import {Col, Flex, Form, message, Modal, Row, Tabs} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useXayDungGoiBaoHiemContext} from "../index.context";
import {
  FormTaoGoiBaoHiem,
  IGoiBaoHiem,
  IModalThemGoiBaoHiemRef,
  ITabCauHinhQuyenLoiRef,
  ITabDieuKhoanBoSungRef,
  listGioiTinhSelect,
  listNghiepVuSelect,
  listTrangThaiGoiBaoHiemSelect,
  ModalThemGoiBaoHiemProps,
} from "./Constant";
import {TabCauHinhQuyenLoi} from "./TabCauHinhQuyenLoi";
import {TabDieuKhoanBoSung} from "./TabDieuKhoanBoSung";
import {ModalCauHinhBenhVien, IModalCauHinhBenhVienRef} from "./ModalCauHinhBenhVien";

const {ma, ten, ma_doi_tac_ql, nv, ma_sp, ngay_ad, gioi_tinh, tuoi_tu, tuoi_toi, ma_ct, trang_thai} = FormTaoGoiBaoHiem;

//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
const ModalThemGoiBaoHiemComponent = forwardRef<IModalThemGoiBaoHiemRef, ModalThemGoiBaoHiemProps>(({}: ModalThemGoiBaoHiemProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataGoiBaoHiem?: CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi) => {
      setIsOpen(true);
      setDisableSubmit(true);
      setTabActive("1");
      console.log("dataGoiBaoHiem", dataGoiBaoHiem);
      if (dataGoiBaoHiem) setChiTietGoiBaoHiem(dataGoiBaoHiem);
    },
    close: () => setIsOpen(false),
  }));

  const {listDoiTac, listSanPham, loading, timKiemPhanTrangBoMaQuyenLoi, updateGoiBaoHiem, timKiemPhanTrangGoiBaoHiem} = useXayDungGoiBaoHiemContext();

  const refTabCauHinhQuyenLoi = useRef<ITabCauHinhQuyenLoiRef>(null);
  const refTabDieuKhoanBoSung = useRef<ITabDieuKhoanBoSungRef>(null);
  const refModalCauHinhBenhVien = useRef<IModalCauHinhBenhVienRef>(null);

  const [chiTietGoiBaoHiem, setChiTietGoiBaoHiem] = useState<CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi | null>(null);

  const [tabActive, setTabActive] = useState<string>("1");
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const [formThongTinGoiBaoHiem] = Form.useForm();
  const watchDoiTacCapDon = Form.useWatch("ma_doi_tac_ql", formThongTinGoiBaoHiem);
  const watchNgiepVu = Form.useWatch("nv", formThongTinGoiBaoHiem);
  const watchSanpham = Form.useWatch("ma_sp", formThongTinGoiBaoHiem);

  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);

  const allFormValues = Form.useWatch([], formThongTinGoiBaoHiem);

  useEffect(() => {
    if (watchDoiTacCapDon && watchNgiepVu && watchSanpham) {
      timKiemPhanTrangBoMaQuyenLoi({
        ma_doi_tac_ql: watchDoiTacCapDon,
        nv: watchNgiepVu,
        ma_sp: watchSanpham,
      });
    }
  }, [watchDoiTacCapDon, watchNgiepVu, watchSanpham]);

  // init form data
  useEffect(() => {
    if (chiTietGoiBaoHiem) {
      fillValueVaoForm();
    }
  }, [chiTietGoiBaoHiem]);

  const fillValueVaoForm = useCallback(async () => {
    const arrFormData = [];
    const arrInputFormThongTinHopDong = Object.keys(FormTaoGoiBaoHiem); //lấy ra key của form
    //chỉ điền những thuộc tính cần hiển thị lên input
    const goi_bh: IGoiBaoHiem = chiTietGoiBaoHiem?.goi_bh || {};
    refTabCauHinhQuyenLoi?.current?.setListQuyenLoi(chiTietGoiBaoHiem?.goi_bh_ct || []); //set List quyền lợi
    refTabDieuKhoanBoSung?.current?.setListDieuKhoan(chiTietGoiBaoHiem?.goi_bh_dkbs || []); //set list diều khoản
    for (const key in goi_bh) {
      if (arrInputFormThongTinHopDong.includes(key)) {
        let value: any = goi_bh[key as keyof IGoiBaoHiem];
        //xử lý các key đặc biệt
        if (key === "ngay_ad") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IHopDongConNguoi,
          value: value,
        });
      }
    }
    //đổ data vào form
    formThongTinGoiBaoHiem.setFields(arrFormData);
  }, [formThongTinGoiBaoHiem, chiTietGoiBaoHiem]);

  //xử lý disable nút Lưu khi form chưa valid
  useEffect(() => {
    formThongTinGoiBaoHiem
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(response => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(error => {
        //xử lý trường hợp khi back từ step 2 về step 1 -> dính lỗi so_hd_g
        if (allFormValues.kieu_hd === "G" && error?.errorFields[0]?.name[0] === "so_hd_g") {
          setDisableSubmit(false); // nếu có lỗi -> cho disable nút Lưu
        } else setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formThongTinGoiBaoHiem, allFormValues]);

  const listSanPhamSelect = useMemo(() => {
    return listSanPham.filter(item => item.ma_doi_tac_ql === watchDoiTacCapDon && item.nv === "NG");
  }, [listSanPham, watchDoiTacCapDon]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietGoiBaoHiem(null);
    formThongTinGoiBaoHiem.resetFields();
    refTabCauHinhQuyenLoi.current?.setListQuyenLoi([]);
    refTabDieuKhoanBoSung.current?.setListDieuKhoan([]);
  }, [formThongTinGoiBaoHiem]);

  const validateTable = (listQuyenLoi: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[]) => {
    // const gioiHanTienNamError: number[] = [];
    const kieuApDungError: number[] = [];
    const nguyenTeSoTienBaoHiemError: number[] = [];
    const tiLeDongChiTraError: number[] = [];
    listQuyenLoi.map((item, index) => {
      // if (item.gh_tien_nam === 0) gioiHanTienNamError.push(index);
      if (item.kieu_ad === "") kieuApDungError.push(index);
      if (item.nt_tien_bh === "") nguyenTeSoTienBaoHiemError.push(index);
      if ((item.tl_dct || 0) < 0 || (item.tl_dct || 0) > 100) tiLeDongChiTraError.push(index);
    });

    // if (tabKey === "1" && gioiHanTienNamError.length > 0) message.warning(`Giới hạn tiền năm ở dòng ${gioiHanTienNamError.join(", ")} phải lớn hơn 0`);
    if (kieuApDungError.length > 0) message.warning(`Vui lòng chọn Kiểu áp dụng ở dòng ${kieuApDungError.join(", ")}`);
    if (nguyenTeSoTienBaoHiemError.length > 0) message.warning(`Vui lòng chọn Nguyên tệ tiền bảo hiểm ở dòng ${nguyenTeSoTienBaoHiemError.join(", ")}`);
    if (tiLeDongChiTraError.length > 0) message.warning(`Tỉ lệ đồng chi trả phải >=0 và <=100 ở dòng ${tiLeDongChiTraError.join(", ")}`);
    if (kieuApDungError.length > 0 || nguyenTeSoTienBaoHiemError.length > 0 || tiLeDongChiTraError.length) return false;
    return true;
  };
  //Bấm Update
  const onConfirm = async () => {
    try {
      await formThongTinGoiBaoHiem.validateFields({validateOnly: false}); //validate form + hiển thị error trên input
      const formValues: ReactQuery.ICapNhatGoiBaoHiemParams = formThongTinGoiBaoHiem.getFieldsValue(); //bổ sung type này vào do ma_kh đang bị định dạng theo string //lấy ra values của form
      console.log("formValues", formValues);
      const listQuyenLoi = refTabCauHinhQuyenLoi.current?.getListQuyenLoi() || [];
      const listDieuKhoan = refTabDieuKhoanBoSung.current?.getListDieuKhoan() || [];
      //validate ở tab 1 trước
      let validate = validateTable(listQuyenLoi);
      if (!validate && tabActive !== "1") setTabActive("1"); //nếu có lỗi table tab 1 -> chuyển sang tab 1
      if (!validate) return;
      //validate ở tab 2 sau
      validate = validateTable(listDieuKhoan);
      if (!validate && tabActive !== "2") setTabActive("2"); //nếu có lỗi table tab 1 -> chuyển sang tab 1
      if (!validate) return;
      const paramsCapNhatGoiBaoHiem: ReactQuery.ICapNhatGoiBaoHiemParams = {
        ...formValues,
        id: chiTietGoiBaoHiem ? chiTietGoiBaoHiem.goi_bh.id : 0,
        ngay_ad: formatDateTimeToNumber(formValues.ngay_ad),
        goi_bh: [], //QUYỀN LỢI
        dkbs: [], //ĐIÈU KHOẢN BỔ SUNG
      };

      //QUYỀN LỢI BẢO HIỂM
      for (let i = 0; i < listQuyenLoi.length; i++) {
        const itemQuyenLoi = listQuyenLoi[i];
        paramsCapNhatGoiBaoHiem.goi_bh?.push({...itemQuyenLoi});
      }

      // QUYỀN LỢI BỔ SUNG
      for (let i = 0; i < listDieuKhoan.length; i++) {
        const itemDieuKhoan = listDieuKhoan[i];
        paramsCapNhatGoiBaoHiem.dkbs?.push({...itemDieuKhoan});
      }
      console.log("paramsCapNhatGoiBaoHiem", paramsCapNhatGoiBaoHiem);
      // return;
      const response = await updateGoiBaoHiem(paramsCapNhatGoiBaoHiem); //cập nhật lại đơn vị chi nhánh
      if (response.data === -1) {
        await timKiemPhanTrangGoiBaoHiem(); //lấy lại danh sách đơn vị chi nhánh
        message.success((!chiTietGoiBaoHiem ? "Tạo mới" : "Cập nhật") + " thành công ");
        setIsOpen(false);
      }
    } catch (error) {
      console.log("onConfirm error", error);
    }
  };

  const onClickThemMoiQuyenLoi = () => {
    if (tabActive === "2") {
      refTabDieuKhoanBoSung.current?.openModalThemQuyenLoi();
    } else if (tabActive === "1") {
      refTabCauHinhQuyenLoi.current?.openModalThemQuyenLoi();
    }
  };

  const onClickCauHinhBenhVien = () => {
    if (chiTietGoiBaoHiem?.goi_bh?.id) {
      refModalCauHinhBenhVien.current?.open(chiTietGoiBaoHiem.goi_bh.id);
    } else {
      message.warning("Vui lòng lưu gói bảo hiểm trước khi cấu hình bệnh viện");
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Button icon={<CheckOutlined />} onClick={onConfirm} loading={loading}>
        Lưu
      </Button>
    );
  };

  const renderFormInputColum = (props: IFormInput, span = 4) => {
    return (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
  };

  const renderFormXayDungGoiBaoHiem = () => {
    return (
      <Form
        form={formThongTinGoiBaoHiem}
        initialValues={{gioi_tinh: "*"}}
        layout="vertical" //Tất cả các input nằm trên 1 dòng
        // onFinish={onSearchApi}
        // className="[&_.ant-form-item]:mb-0" // cho margin-bottom của form = 0
      >
        <Row gutter={16}>
          {renderFormInputColum(ma)}
          {renderFormInputColum(ten)}
          {renderFormInputColum(
            {
              ...ma_doi_tac_ql,
              options: listDoiTac,
              onChange: () => {
                formThongTinGoiBaoHiem.setFieldValue("ma_sp", undefined);
              },
            },
            8,
          )}
          {renderFormInputColum({
            ...nv,
            options: listNghiepVuSelect,
          })}
          {renderFormInputColum({...ma_sp, options: listSanPhamSelect})}
        </Row>
        <Row gutter={16}>
          {renderFormInputColum(ngay_ad)}
          {renderFormInputColum({...gioi_tinh, options: listGioiTinhSelect})}
          {renderFormInputColum(tuoi_tu)}
          {renderFormInputColum(tuoi_toi)}
          {renderFormInputColum(ma_ct)}
          {renderFormInputColum({...trang_thai, options: listTrangThaiGoiBaoHiemSelect})}
        </Row>
      </Form>
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="center">
      <Modal
        className="modal-them-goi-bao-hiem-con-nguoi"
        title={
          <HeaderModal
            title={chiTietGoiBaoHiem ? `Chi tiết gói bảo hiểm ${chiTietGoiBaoHiem.goi_bh.ten}` : "Tạo mới gói bảo hiểm"}
            trang_thai_ten={chiTietGoiBaoHiem?.goi_bh?.trang_thai === "D" ? "Đang sử dụng" : "Chưa sử dụng"}
            trang_thai={chiTietGoiBaoHiem?.goi_bh?.trang_thai}
          />
        }
        centered
        open={isOpen}
        maskClosable={false}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={"97vw"}
        styles={{
          body: {
            height: "78vh",
          },
        }}
        footer={renderFooter}>
        {renderFormXayDungGoiBaoHiem()}
        <Tabs
          defaultActiveKey="1"
          activeKey={tabActive}
          tabBarExtraContent={{
            right: (
              <div style={{display: "flex", gap: "8px"}}>
                <Button onClick={onClickCauHinhBenhVien} icon={<SettingOutlined />} disabled={!chiTietGoiBaoHiem?.goi_bh?.id}>
                  Cấu hình bệnh viện
                </Button>
                <Button onClick={onClickThemMoiQuyenLoi} icon={<PlusCircleOutlined />}>
                  Thêm mới quyền lợi vào gói
                </Button>
              </div>
            ),
          }}
          items={[
            {
              key: "1",
              label: "Cấu hình quyền lợi",
              children: <TabCauHinhQuyenLoi ref={refTabCauHinhQuyenLoi} />,
            },
            {
              key: "2",
              label: "Điều khoản bổ sung",
              children: <TabDieuKhoanBoSung ref={refTabDieuKhoanBoSung} />,
            },
          ]}
          onChange={setTabActive}
        />
      </Modal>
      <ModalCauHinhBenhVien ref={refModalCauHinhBenhVien} />
    </Flex>
  );
});

ModalThemGoiBaoHiemComponent.displayName = "ModalThemGoiBaoHiemComponent";
export const ModalThemGoiBaoHiem = memo(ModalThemGoiBaoHiemComponent, isEqual);

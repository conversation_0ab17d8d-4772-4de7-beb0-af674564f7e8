import {ReactQuery} from "@src/@types";
import {ROUTER_PATHS, URL_API} from "@src/constants";
import {useProfile} from "@src/hooks";
import {useMenuNguoiDung} from "@src/hooks/menuNguoiDungStore";
import {navigateTo} from "@src/utils";
import {message} from "antd";
import {AuthEndpoint, axiosInstance} from "..";

/*
==== THAM SỐ REQUEST ====
- ILayDanhSachNguoiDungTheoNhomParams : LAY_DANH_SACH_MENU_NGUOI_DUNG_THEO_NHOM
- ILayDanhSachDoiTacParams : TIM_KIEM_DANH_SACH_DOI_TAC_PHAN_TRANG
- IChiTiethDoiTacParams : GET_CHI_TIET_DOI_TAC
- actionCode : api nào cũng phải truyền actionCode lên để xử lý trong before request
 */
type CommonExecuteParams = ReactQuery.IPhanTrang & {actionCode: string} & ReactQuery.ILayDanhSachNguoiDungTheoNhomParams &
  ReactQuery.ILayDanhSachDoiTacParams &
  ReactQuery.IChiTiethDoiTacParams &
  ReactQuery.IChiTietPhongBanParams &
  ReactQuery.IUpdatePhongBanParams &
  ReactQuery.ILayDanhSachPhongBanPhanTrangParams &
  ReactQuery.IUpdateDoiTacParams &
  ReactQuery.IChiTietDonViChiNhanhParams &
  ReactQuery.ILayDanhSachTaiKhoanNguoiDungPhanTrangParams &
  ReactQuery.ILayDanhMucPhongBanParams & // LIET_KE_DANH_SACH_PHONG_BAN
  ReactQuery.ILayDanhSachChucNangPhanTrangParams & // GET_DANH_SACH_CHUC_NANG_PHAN_TRANG
  ReactQuery.IChiTietChucNangParams & // GET_CHI_TIET_CHUC_NANG
  ReactQuery.IUpdateChucNangParams &
  ReactQuery.ILayDanhSachChucDanhPhanTrangParams & // LUU_CAP_NHAT_CHUC_NANG
  ReactQuery.ILayDanhSachKhachHangPhanTrangParams &
  ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams &
  ReactQuery.ILietKeSanPhamParams &
  ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams &
  ReactQuery.IChiTietDanhMucDaiLyParams &
  ReactQuery.IUpdateDaiLyParams &
  ReactQuery.ILietKeChuongTrinhBaoHiemParams &
  ReactQuery.ITimKiemPhanTrangPhuongThucKhaiThacParams &
  ReactQuery.IlayChiTietPhuongThucKhaiThacParams &
  ReactQuery.IUpdatePhuongThucKhaiThacParams &
  ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams &
  ReactQuery.IUpdateDanhMucSanPhamParams &
  ReactQuery.IChiTietDanhMucSanPhamParams &
  ReactQuery.IUpdateHopDongParams &
  ReactQuery.ILayDanhSachHopDongConNguoiPhanTrangParams &
  ReactQuery.ILietKeDanhSachDaiLyParams &
  ReactQuery.ILietKeDonViBoiThuongTPAParams &
  ReactQuery.IChiTietChucDanhParams &
  ReactQuery.ILietKePhuongThucKhaiThacParams &
  ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams &
  ReactQuery.IChiTietBoMaQuyenLoiParams &
  ReactQuery.IUpdateBoMaQuyenLoiParams &
  ReactQuery.IChiTietDoiTuongBaoHiemXeParams &
  ReactQuery.IChiTietHeThongMenuParams &
  ReactQuery.ICapNhatHeThongMenuParams &
  ReactQuery.ITimkiemPhanTrangHeThongMenuParams &
  ReactQuery.ITimKiemPhanTrangBoMaNguyenTeParams &
  ReactQuery.IlayChiTietBoMaNguyenTeParams &
  ReactQuery.IUpdateBoMaNguyenTeParams &
  ReactQuery.ITimKiemPhanTrangDanhMucNganHangParams &
  ReactQuery.ICapNhatDanhMucNganHangParams &
  ReactQuery.IChiTietDanhMucNganHangParams &
  ReactQuery.IChiTietChuongTrinhBaoHiemParams &
  ReactQuery.ICapNhatChuongTrinhBaoHiemParams &
  ReactQuery.ITimKiemPhanTrangChuongTrinhBaoHiemParams &
  ReactQuery.IXoaGoiBaoHiemKhoiCTBHParams &
  ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams &
  ReactQuery.ICapNhatGoiBaoHiemParams &
  ReactQuery.IChiTietGoiBaoHiemParams &
  ReactQuery.ITimKiemPhanTrangChiNhanhNganHangParams & //NXH
  ReactQuery.IChiTietChiNhanhNganHangParams & //NXH
  ReactQuery.ICapNhatChiNhanhNganHangParams & //NXH
  ReactQuery.ITimKiemPhanTrangDanhMucLoaiXeParams & //NXH
  ReactQuery.ICapNhatDanhMucLoaiXeParams & //NXH
  ReactQuery.IChiTietDanhMucLoaiXeParams & //NXH
  ReactQuery.ITimKiemPhanTrangNhomHangMucXeParams & //NXH
  ReactQuery.IChiTietNhomHangMucXeParams & //NXH
  ReactQuery.ICapNhatNhomHangMucXeParams & //NXH
  ReactQuery.ITimKiemPhanTrangHangMucXeParams & //NXH
  ReactQuery.IChiTietHangMucXeParams & //NXH
  ReactQuery.ICapNhatHangMucXeParams & //NXH
  ReactQuery.ITimKiemPhanTrangMucDoTonThatXeParams & //NXH
  ReactQuery.IChiTietMucDoTonThatXeParams & //NXH
  ReactQuery.ICapNhatMucDoTonThatXeParams & //NXH
  ReactQuery.IChiTietGoiBaoHiemParams &
  ReactQuery.IUpdateKyThanhToanParams &
  ReactQuery.IChiTietDoiTuongBaoHiemXeParams &
  ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams &
  ReactQuery.ILayThongTinThanhToanCuaHopDongBaoHiemParams &
  ReactQuery.IChiTietKyThanhToanParams &
  //ReactQuery.ILayThongTinDongCuaHopDongBaoHiemParams &
  ReactQuery.IChiTietThongTinCauHinhDongBHParams &
  ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & // TINH THANH
  ReactQuery.IChiTietDanhMucTinhThanhParams & // TINH THANH
  ReactQuery.ICapNhatDanhMucTinhThanhParams & // TINH THANH
  ReactQuery.ITimKiemPhanTrangDanhMucQuanHuyenParams & // QUẬN HUYỆN
  ReactQuery.IChiTietDanhMucQuanHuyenParams & // QUẬN HUYỆN
  ReactQuery.ICapNhatDanhMucQuanHuyenParams & // QUẬN HUYỆN
  ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams & // PHƯỜNG XÃ
  ReactQuery.IChiTietDanhMucPhuongXaParams & // PHƯỜNG XÃ
  ReactQuery.ICapNhatDanhMucPhuongXaParams & // PHƯỜNG XÃ
  ReactQuery.ITimKiemPhanTrangDanhMucChauLucParams & // CHÂU LỤC
  ReactQuery.IChiTietDanhMucChauLucParams & // CHÂU LỤC
  ReactQuery.ICapNhatDanhMucChauLucParams & // CHÂU LỤC
  ReactQuery.ITimKiemPhanTrangDanhMucKhuVucParams & // KHU VỰC
  ReactQuery.IChiTietDanhMucKhuVucParams & // KHU VỰC
  ReactQuery.ICapNhatDanhMucKhuVucParams & // KHU VỰC
  ReactQuery.ITimKiemPhanTrangDanhMucQuocGiaParams & // QUỐC GIA
  ReactQuery.IChiTietDanhMucQuocGiaParams & // QUỐC GIA
  ReactQuery.ICapNhatDanhMucQuocGiaParams & // QUỐC GIA
  ReactQuery.ITimKiemPhanTrangDanhMucBenhVienParams & // DANH MỤC BỆNH VIỆN
  ReactQuery.IChiTietDanhMucBenhVienParams & // DANH MỤC BỆNH VIỆN
  ReactQuery.ICapNhatDanhMucBenhVienParams & // DANH MỤC BỆNH VIỆN
  ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams &
  ReactQuery.IChiTietThongTinCauHinhDongBHParams &
  ReactQuery.IUpdateCauHinhDongBHParams &
  ReactQuery.IChiTietThongTinCauHinhTaiBHParams &
  ReactQuery.IUpdateCauHinhTaiBHParams &
  ReactQuery.IUpdateDoiTuongApDungDongBHParams &
  ReactQuery.ILietKeDanhSachNguoiDuyetParams &
  ReactQuery.ITrinhPheDuyetHopDongParams &
  ReactQuery.ITimKiemPhanTrangHopDongTrinhDuyetParams &
  ReactQuery.IUpdateDoiTuongApDungTaiBHParams &
  ReactQuery.ITimKiemPhanTrangNguoiDuocBaoHiemHopDongConNguoi &
  ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi &
  ReactQuery.ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi &
  ReactQuery.IXemChiTietHopDongTrinhDuyetParams &
  ReactQuery.ICapNhatHopDongTrinhDuyetParams &
  ReactQuery.ITimKiemPhanTrangCauHinhMauHopDongParams &
  ReactQuery.IChiTietCauHinhMauHopDongParams &
  ReactQuery.ICapNhatCauHinhMauHopDongParams &
  ReactQuery.ITimKiemPhanTrangCauHinhMauGCNParams &
  ReactQuery.IChiTietCauHinhMauGCNParams &
  ReactQuery.ICapNhatCauHinhMauGCNParams &
  ReactQuery.IGetFileThumbnailParams &
  ReactQuery.IUploadFileTheoDoiTuongXeParams &
  ReactQuery.ILietKeHangMucXeTheoNhomParams &
  ReactQuery.ILuuHangMucTonThatParams &
  ReactQuery.ILuuDanhGiaTonThatParams &
  ReactQuery.ILietKeDieuKhoanNguoiDuocBaoHiemParams &
  ReactQuery.ILuuDieuKhoanNguoiDuocBaoHiemParams &
  ReactQuery.ILietKeDieuKhoanBoSungNguoiDuocBaoHiemParams &
  ReactQuery.ILuuDieuKhoanBoSungNguoiDuocBaoHiemParams &
  ReactQuery.ILietKeDanhSachNguoiPhuThuocHopDongConNguoiParams &
  ReactQuery.IChiTietNguoiPhuThuocHopDongConNguoiParams &
  ReactQuery.ICapNhatNguoiPhuThuocHopDongConNguoiParams &
  ReactQuery.ITimKiemPhanTrangDanhSachNhomPhanCapDuyetParams &
  ReactQuery.IChiTietNhomPhanCapDuyetParams &
  ReactQuery.IUpdateNhomPhanCapDuyetParams &
  ReactQuery.IUpdateTaiKhoanNguoiDungParams &
  ReactQuery.IUpdateCauHinhPhanCapPheDuyetParams &
  ReactQuery.IChiTietCauHinhPhanCapPheDuyetCTParams &
  ReactQuery.IUpdateCauHinhPhanCapPheDuyetCTParams &
  ReactQuery.ILietKeCauHinhPhanCapPheDuyetCTParams &
  ReactQuery.IDeleteCauHinhPhanCapPheDuyetCTParams &
  ReactQuery.ILietKeCauHinhPhanCapPheDuyetParams &
  ReactQuery.ILietKeCauHoiApDungParams &
  ReactQuery.IDeleteCauHoiApDungParams &
  ReactQuery.ILietKeCauHoiParams &
  ReactQuery.IChiTietCauHoiParams &
  ReactQuery.IUpdateCauHoiParams &
  ReactQuery.IDeleteCauHoiParams &
  ReactQuery.IExportExcelParams &
  ReactQuery.ILietKeDanhSachTyLeHoaHongParams &
  ReactQuery.ICapNhatCauHinhTyLeHoaHongParams &
  ReactQuery.ILayCayTyLeHoaHongParams &
  ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams &
  ReactQuery.IChiTietChucNangTheoVaiTroParams &
  ReactQuery.ICapNhatChucNangTheoVaiTroParams &
  ReactQuery.IDeleteChucNangTheoVaiTroParams &
  ReactQuery.ILayDanhSachNhomChucNangPhanTrangParams &
  ReactQuery.IChiTietNhomChucNangParams &
  ReactQuery.IUpdateNhomChucNangParams &
  ReactQuery.IDeleteChucNangTheoNhomParams &
  ReactQuery.ILietKeCauHoiDanhGiaSucKhoeConNguoiParams &
  ReactQuery.ILuuDanhGiaSucKhoeConNguoiParams &
  ReactQuery.ITimKiemPhanTrangDanhSachNghiepVuParams &
  ReactQuery.IChiTietDanhMucNghiepVuParams &
  ReactQuery.IUpdateDanhMucNghiepVuParams &
  ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams &
  ReactQuery.IChiTietNhomDoiTuongParams &
  ReactQuery.IUpdateNhomDoiTuongParams &
  ReactQuery.ITimKiemBenhVienParams &
  ReactQuery.ILayDanhSachBenhVienDaLuuParams;

/* 
==== RESPONSE ====
- IMenuNguoiDungTheoNhom : LAY_DANH_SACH_MENU_NGUOI_DUNG_THEO_NHOM
- IDoiTac : DANH_SACH_DOI_TAC
- IPhongBan: DANH_SACH_PHONG_BAN
- IPhongBan DANH_SACH_PHONG_BAN 
- IDoiTac : TIM_KIEM_DANH_SACH_DOI_TAC_PHAN_TRANG
- IChiNhanh : LIET_KE_DANH_SACH_CHI_NHANH
*/
type CommonExecuteResponse = {
  output?: {
    tong_so_dong?: number;
    so_hd?: string;
    so_id?: number;
    id?: number;
  };
  data: Array<CommonExecute.Execute.IMenuNguoiDungTheoNhom> &
    Array<CommonExecute.Execute.IPhongBan> & {data: Array<CommonExecute.Execute.IDoiTac>; tong_so_dong: number} & CommonExecute.Execute.IPhongBan &
    Array<CommonExecute.Execute.IChiNhanh> & {data: CommonExecute.Execute.IDoiTac} & {data: Array<CommonExecute.Execute.IDanhSachPhongBanPhanTrang>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IChiNhanh>;
      tong_so_dong: number;
    } & {data: CommonExecute.Execute.IChiNhanh} & {data: Array<CommonExecute.Execute.IDanhSachTaiKhoanNguoiDungPhanTrang>; tong_so_dong: number} & CommonExecute.Execute.IChiTietTaiKhoanNguoiDung &
    CommonExecute.Execute.IChiTietChucNang & {data: Array<CommonExecute.Execute.IChiTietChucDanh>; tong_so_dong: number} & {data: Array<CommonExecute.Execute.IKhachHang>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IDoiTacNhanVien>;
      tong_so_dong: number;
    } & {data: Array<CommonExecute.Execute.ISanPham>} & {data: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>} & {data: Array<CommonExecute.Execute.INhaBaoHiemTPA>} & {
      data: Array<CommonExecute.Execute.IDanhMucDaiLy>;
      tong_so_dong: number;
    } & {data: Array<CommonExecute.Execute.IChiTietDanhMucDaiLy>} & {data: Array<CommonExecute.Execute.IDanhSachPhuongThucKhaiThac>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IChiTietPhuongThucKhaiThac>;
    } & {data: Array<CommonExecute.Execute.IDanhMucSanPham>; tong_so_dong: number} & {data: Array<CommonExecute.Execute.IHopDongConNguoi>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IDanhSachBoMaQuyenLoi>;
      tong_so_dong: number;
    } & CommonExecute.Execute.IChiTietBoMaQuyenLoi & {data: number} & number &
    CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi & {data: Array<CommonExecute.Execute.IHeThongMenu>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IDanhSachBoMaNguyenTe>;
      tong_so_dong: number;
    } & {data: Array<CommonExecute.Execute.IChiTietBoMaNguyenTe>} & {
      data: Array<CommonExecute.Execute.IDanhMucNganHang>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IGoiBaoHiem>;
    } & {
      data: Array<CommonExecute.Execute.IGoiBaoHiemConNguoi>;
      tong_so_dong: number;
    } & CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi &
    CommonExecute.Execute.IHopDongXe &
    CommonExecute.Execute.IKyThanhToan & {
      data: Array<CommonExecute.Execute.IDanhMucTinhThanh>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucQuanHuyen>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucPhuongXa>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucChauLuc>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucKhuVuc>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucQuocGia>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucBenhVien>;
      tong_so_dong: number;
    } & CommonExecute.Execute.IKyThanhToan &
    CommonExecute.Execute.ITaiBaoHiem & {
      data: Array<CommonExecute.Execute.INguoiPheDuyetHopDong>;
    } & CommonExecute.Execute.IDongBaoHiem & {
      data: Array<CommonExecute.Execute.INhomHangMucXe>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IHangMucXe>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.ICauHinhMauHopDong>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.ICauHinhMauGCN>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IMucDoTonThatXe>;
      tong_so_dong: number;
    } & {data: CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi[]; tong_so_dong: number} & CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi & {
      data: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
    } & {data: CommonExecute.Execute.IChiTietNguoiPhuThuoc[]} & {data: Array<CommonExecute.Execute.INhomPhanCapDuyet>; tong_so_dong: number} & CommonExecute.Execute.INhomPhanCapDuyet & {
      data: Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyet>;
      tong_so_dong: number;
    } & {data: Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT>} & {data: Array<CommonExecute.Execute.ICauHoiApDung>} & {data: Array<CommonExecute.Execute.ICauHoi>} & {
      data: Array<CommonExecute.Execute.IChiTietTaiKhoanNguoiDung> & {data: Array<CommonExecute.Execute.IChiTietNguoiSuDung>};
    } & {data: Array<CommonExecute.Execute.ICauHinhTyLeHoaHong>} & {data: Array<CommonExecute.Execute.ICayTyLeHoaHong>} & {data: Array<CommonExecute.Execute.IChucNangTheoVaiTro>} & {
      data: Array<CommonExecute.Execute.IChiTietChucNangTheoVaiTro>;
    } & {data: Array<CommonExecute.Execute.INhomChucNang>} & {data: Array<CommonExecute.Execute.IChiTietNhomChucNang>} & {data: Array<CommonExecute.Execute.IDanhSachNhomChucNangPhanTrang>} & {
      data: Array<CommonExecute.Execute.IVaiTroChucNang>;
    } & {data: Array<CommonExecute.Execute.IDanhMucNghiepVu>; tong_so_dong: number} & {data: Array<CommonExecute.Execute.INhomDoiTuong>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IBenhVien>;
      tong_so_dong: number;
    };
};

export const getCommonExecute = async (params: CommonExecuteParams): Promise<CommonExecuteResponse> => {
  try {
    const response = await axiosInstance.post(URL_API.COMMON_EXECUTE, params);
    return response;
  } catch (error) {
    return handleError(error, params);
  }
};

const handleError = async (error: any, params: CommonExecuteParams) => {
  console.log("handleError", error);
  if (error.status === 400) {
    if (error.response?.data?.error_message === "Thông tin token không hợp lệ hoặc đã bị hết hạn.") {
      //nếu token hết hạn -> gọi api refresh lại access_token
      const {profile, setToken} = useProfile.getState();
      const responseRefreshAccessToken = await AuthEndpoint.rereshAccessToken({token: profile.token, refresh_token: profile.refresh_token});
      console.log("responseRefreshAccessToken");
      console.log(responseRefreshAccessToken);
      //cập nhật lại token
      if (typeof responseRefreshAccessToken === "string") {
        setToken(responseRefreshAccessToken);
        return await getCommonExecute(params);
      } else if (responseRefreshAccessToken.status === 400) {
        const profileTmp = profile;
        profileTmp.token = "";
        useProfile.getState().setProfile(profileTmp);
        useMenuNguoiDung.getState().setMenuNguoiDung([]);
        navigateTo(ROUTER_PATHS.DANG_NHAP);
        message.info("Phiên đăng nhập hết hạn. Vui lòng đăng nhập lại");
      }
    } else if (error.response?.data?.error_message) {
      message.warning(error.response?.data?.error_message);
    }
  }
  return error;
};

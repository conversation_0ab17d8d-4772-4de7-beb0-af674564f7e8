import {ConfigProvider, theme} from "antd";
import dayjs from "dayjs";
import "dayjs/locale/vi";
import viVN from "antd/es/locale/vi_VN";
import {useMemo} from "react";
import {I18nextProvider} from "react-i18next";
import {QueryClientProvider} from "react-query";
import {<PERSON>rowserRouter} from "react-router-dom";
import {ErrorBoundary} from "./components";
import i18n from "./configs/i18n";
import {queryClient} from "./configs/react-query";
import {Navigation} from "./configs/router";
import {DARK_THEME_CONFIGS, LIGHT_THEME_CONFIGS} from "./configs/theme";
import {ThemeMode, useSetting} from "./hooks";

dayjs.locale("vi");
const App = () => {
  const {themeMode} = useSetting();
  const isDarkTheme = useMemo(() => themeMode === ThemeMode.dark, [themeMode]);

  return (
    <ConfigProvider
      theme={{
        ...theme,
        // TOKEN : DÙNG ĐỂ CẤU HÌNH STYLE CHO TOÀN BỘ APP
        token: isDarkTheme ? DARK_THEME_CONFIGS : LIGHT_THEME_CONFIGS,
        algorithm: isDarkTheme ? [theme.darkAlgorithm] : [theme.defaultAlgorithm],
        // components : DÙNG ĐỂ CẤU HÌNH STYLE CHO TOÀN BỘ COMPONENT
        components: {
          // Button: {
          //   borderRadius: 5, // Cập nhật radius thành 5px
          //   // borderColorDisabled: '#0AC275',
          // },
          Input: {
            inputFontSize: 12,
            // borderRadius: 5,
          },
          DatePicker: {
            // inputFontSize: 12,
            // borderRadius: 5,
          },
          Select: {
            // optionFontSize: 12,
            // borderRadius: 5,
          },
        },
      }}
      locale={viVN}>
      <I18nextProvider i18n={i18n}>
        <QueryClientProvider client={queryClient}>
          <ErrorBoundary>
            <BrowserRouter>
              <Navigation />
            </BrowserRouter>
          </ErrorBoundary>
        </QueryClientProvider>
      </I18nextProvider>
    </ConfigProvider>
  );
};

export default App;

.sub-menu-custom > ul.ant-menu-sub {
  background: transparent !important;
}

.no-arrow-submenu .ant-menu-submenu-arrow {
  display: none !important;
}

.ant-menu-item-group-list {
  padding-left: 16px !important;
  margin-left: 24px;
}

// Override indentation when sidebar is collapsed - moved to main collapsed styles section

.ant-menu-title-label {
  display: flex !important; /* Sử dụng flex để căn chỉnh icon và text */
  align-items: center !important; /* Căn giữa theo chiều dọc */
  max-width: 100%; /* hoặc giá trị cụ thể như 150px */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* Đảm bảo text không wrap */
  text-decoration: none !important; /* Loại bỏ underline */
}

// Submenu label styling - cho phép hiển thị đầy đủ hơn
.submenu-label {
  max-width: calc(100% - 24px) !important; /* Trừ đi khoảng cách cho icon */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  line-height: 1.4 !important; /* Cải thiện line height */
  vertical-align: middle !important; /* Căn giữa với icon */
}

// Submenu icon styling for expanded state
.layout-dashboard .ant-layout-sider.sider-primary .ant-menu-submenu .ant-menu-item-group-list {
  .ant-menu-item {
    a {
      display: flex !important;
      align-items: center !important;
      padding: 8px 16px !important;
      text-decoration: none !important;
    }
  }

  .submenu-icon-expanded {
    font-size: 12px !important;
    // margin-right: 8px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: color 0.15s ease-in-out !important;
    flex-shrink: 0 !important; // Prevent icon from shrinking
    width: 16px !important; // Fixed width for consistent alignment
    height: 16px !important; // Fixed height for consistent alignment
  }

  .submenu-label {
    display: inline-block !important;
    line-height: 1.4 !important; // Better line height for text alignment
    vertical-align: middle !important;
  }
}

// Ensure submenu items are properly aligned when sidebar is expanded
.layout-dashboard.sidebar-expanded .ant-layout-sider.sider-primary .ant-menu-submenu .ant-menu-item-group-list {
  .ant-menu-item {
    a.ant-menu-title-label {
      display: flex !important;
      align-items: center !important;
      padding: 8px 16px !important;
      min-height: 32px !important; // Consistent height for all items

      .submenu-icon-expanded {
        // margin-right: 8px !important;
        flex-shrink: 0 !important;
      }

      .submenu-label {
        flex: 1 !important;
        text-align: left !important;
      }
    }
  }
}

// Highlight parent menu when child is active
.layout-dashboard .ant-layout-sider.sider-primary .ant-menu-submenu.ant-menu-submenu-open .ant-menu-submenu-title {
  background-color: rgba(175, 206, 117, 0.3) !important; // Using green[70] with transparency

  .icon {
    background: rgba(175, 206, 117, 0.8) !important; // Using green[70] with higher opacity
    border-radius: 6px;
    color: #1a5f1a !important; // Darker green color when highlighted
  }
}

// Dark theme support
.dark .layout-dashboard .ant-layout-sider.sider-primary .ant-menu-submenu.ant-menu-submenu-open .ant-menu-submenu-title {
  background-color: rgba(255, 102, 166, 0.15) !important; // Using pink[40] for dark theme

  .icon {
    background: rgba(255, 102, 166, 0.3) !important; // Using pink[40] with higher opacity
    border-radius: 6px;
    color: #8b0040 !important; // Darker pink color when highlighted in dark theme
  }
}

// Ensure menu functionality is preserved
.layout-dashboard .ant-layout-sider.sider-primary .ant-menu-submenu {
  // Only show submenu content when parent is open (for expanded state)
  &:not(.ant-menu-submenu-open) .ant-menu-sub {
    display: none !important;
  }

  .ant-menu-submenu-title {
    cursor: pointer;

    &:hover:not(.ant-menu-submenu-open .ant-menu-submenu-title) {
      background-color: rgba(0, 0, 0, 0.04);
    }

    // Ensure icons are properly styled
    .icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: background-color 0.15s ease-in-out;
      color: #2c3e50 !important; // Darker blue-gray color for better visibility

      svg {
        fill: currentColor !important;
      }

      &:hover {
        color: #1a252f !important; // Even darker on hover
      }
    }
  }
}

// Additional highlight for better visual feedback
.layout-dashboard .ant-layout-sider.sider-primary .ant-menu-submenu.ant-menu-submenu-open {
  .ant-menu-submenu-title {
    &:hover {
      background-color: rgba(175, 206, 117, 0.2) !important; // Slightly darker on hover
    }
  }
}

// Dark theme hover
.dark .layout-dashboard .ant-layout-sider.sider-primary .ant-menu-submenu.ant-menu-submenu-open {
  .ant-menu-submenu-title {
    &:hover {
      background-color: rgba(255, 102, 166, 0.2) !important; // Slightly darker on hover
    }
  }
}

// Ensure parent menu styling is preserved and enhanced
.layout-dashboard .ant-layout-sider.sider-primary .ant-menu-submenu {
  // Parent menu title styling
  .ant-menu-submenu-title {
    // Ensure proper spacing and alignment
    display: flex !important;
    align-items: center !important;

    // Icon styling for parent menus
    .icon {
      margin-right: 8px !important;
      flex-shrink: 0 !important; // Prevent icon from shrinking
    }

    // Label styling for parent menus
    .label {
      flex: 1 !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
  }
}

.brand {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.15s ease-in-out;
  padding: 0 !important; // Consistent padding for both states
  min-height: 56px; // Reduced height for better spacing

  &.collapsed {
    justify-content: center;
    padding: 12px 0; // Same padding for consistent alignment
    min-height: 56px; // Same height as expanded state

    span {
      opacity: 0;
      display: none; // Use display none instead of width animation
      transition: opacity 0.15s ease-in-out;
    }

    img {
      width: 32px !important; // Keep same size as expanded for consistency
      height: 32px !important; // Fixed height for perfect alignment
      object-fit: contain; // Maintain aspect ratio
    }
  }

  img {
    transition: none; // No animation for logo to prevent jumping
    width: 32px; // Fixed width for consistency
    height: 32px; // Fixed height for perfect alignment
    object-fit: contain; // Maintain aspect ratio
  }

  span {
    transition: opacity 0.15s ease-in-out;
    white-space: nowrap;
    // Remove width animation to prevent jumping
  }
}

// Menu label transitions
.label {
  transition: opacity 0.15s ease-in-out;
  white-space: nowrap;

  &.collapsed {
    opacity: 0;
    display: none; // Use display none to prevent layout jumping
  }
}

// Sidebar collapsed styles - increased specificity to override global CSS
.layout-dashboard.sidebar-collapsed .ant-layout-sider.sider-primary {
  // Submenu icon centering when collapsed
  .ant-menu-submenu-title {
    justify-content: center !important;
    padding-left: 20px !important; // Balanced left padding
    padding-right: 10px !important; // Equal right padding for perfect centering
    text-align: center !important;

    .anticon, .icon {
      margin-right: 0 !important;
      margin-left: 0 !important;
    }
  }

  // Center all menu items when collapsed
  .ant-menu-submenu {
    text-align: center !important;

    // Only show submenu content when parent is open
    &:not(.ant-menu-submenu-open) .ant-menu-sub {
      display: none !important;
    }

    // Ensure submenu content is visible when collapsed AND parent is open
    &.ant-menu-submenu-open .ant-menu-sub {
      display: block !important;
    }
  }

  // Override indentation for menu item group list - override global CSS
  .ant-menu-submenu .ant-menu-item-group-list {
    padding-left: 0 !important; // Override global padding-left: 16px
    margin-left: 0 !important; // Override global margin-left: 24px
    text-align: center !important;
    display: block !important; // Ensure submenu items are visible
  }

  // Child menu items styling - increased specificity
  .ant-menu-submenu .ant-menu-item-group-list .ant-menu-item {
    padding-left: 8px !important; // Reduced padding for more compact layout
    padding-right: 8px !important; // Equal right padding for centering
    text-align: center !important;
    margin-bottom: 4px !important; // Reduced margin between items

    a {
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      padding: 6px 0 !important; // Reduced padding for more compact layout
      width: 100% !important; // Ensure full width for clickability

      &:before {
        display: none !important; // Hide bullet points
      }

      .submenu-icon {
        display: inline-flex !important;
        justify-content: center !important;
        align-items: center !important;
        width: 16px !important;
        height: 16px !important;
        font-size: 12px !important;
      }

      // Hide expanded state elements when collapsed
      .submenu-icon-expanded,
      .submenu-label {
        display: none !important;
      }
    }
  }

  // Hide submenu arrows when collapsed
  .ant-menu-submenu-arrow {
    display: none !important;
  }

  // Style for submenu icons when collapsed
  .submenu-icon {
    color: #333 !important;
    font-size: 14px !important; // Smaller size for more compact layout
    display: inline-flex !important; // Match parent icons
    justify-content: center !important;
    align-items: center !important;
    width: 24px !important; // Smaller width for more compact layout
    height: 24px !important; // Smaller height for more compact layout
    transition: color 0.15s ease-in-out !important;
    border-radius: 4px !important; // Smaller border radius
    background-color: rgba(0, 0, 0, 0.05) !important; // Subtle background

    &:hover {
      background-color: rgba(0, 0, 0, 0.1) !important; // Darker background on hover
    }
  }
}

// CSS tối ưu cho màu icon menu - đồng bộ màu xám #999999
.layout-dashboard .ant-layout-sider.sider-primary .ant-menu-submenu {
  // Icon menu cha - màu xám
  .ant-menu-submenu-title .icon,
  .ant-menu-submenu-title .icon svg,
  .ant-menu-submenu-title .icon svg path {
    color: #999999 !important;
    fill: #999999 !important;
  }

  // Icon menu con - màu xám
  .ant-menu-item-group-list .ant-menu-item a .submenu-icon,
  .ant-menu-item-group-list .ant-menu-item a .submenu-icon-expanded,
  .ant-menu-item-group-list .ant-menu-item a .submenu-icon svg,
  .ant-menu-item-group-list .ant-menu-item a .submenu-icon-expanded svg,
  .ant-menu-item-group-list .ant-menu-item a .submenu-icon svg path,
  .ant-menu-item-group-list .ant-menu-item a .submenu-icon-expanded svg path {
    color: #999999 !important;
    fill: #999999 !important;
  }

  // Icon menu con active - màu đen
  .ant-menu-item-group-list .ant-menu-item.ant-menu-item-selected a .submenu-icon,
  .ant-menu-item-group-list .ant-menu-item.ant-menu-item-selected a .submenu-icon-expanded,
  .ant-menu-item-group-list .ant-menu-item.ant-menu-item-selected a .submenu-icon svg,
  .ant-menu-item-group-list .ant-menu-item.ant-menu-item-selected a .submenu-icon-expanded svg,
  .ant-menu-item-group-list .ant-menu-item.ant-menu-item-selected a .submenu-icon svg path,
  .ant-menu-item-group-list .ant-menu-item.ant-menu-item-selected a .submenu-icon-expanded svg path {
    color: #000000 !important;
    fill: #000000 !important;
  }
}

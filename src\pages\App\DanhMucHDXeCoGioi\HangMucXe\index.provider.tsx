import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {message} from "antd";
import {isEqual} from "lodash";

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {useCommonExecute} from "@src/services/react-queries";
import {defaultPaginationTableProps} from "@src/hooks";

import HangMucXeContext from "./index.context";
import {IHangMucXeProvider} from "./index.model";

/**
 * Component này wrap các component con và cung cấp context với state và methods
 */
const HangMucXeProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;

  // ===== HOOKS VÀ STATE MANAGEMENT =====
  const mutateUseCommonExecute = useCommonExecute(); // Hook để gọi API chung

  // ===== FILTER PARAMETERS STATE - QUẢN LÝ THAM SỐ TÌM KIẾM VÀ PHÂN TRANG =====
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangHangMucXeParams & ReactQuery.IPhanTrang>({
    ma: "",
    ten: "",
    nv: "",
    loai: "",
    nhom: "",
    vi_tri: "",
    trang_thai: "",
    trang: 1,
    so_dong: defaultPaginationTableProps.defaultPageSize,
  });

  // DATA STATES
  const [tongSoDong, setTongSoDong] = useState<number>(0); // Tổng số dòng từ API
  const [listHangMucXe, setListHangMucXe] = useState<Array<CommonExecute.Execute.IHangMucXe>>([]); // Danh sách hạng mục xe chính
  const [listNhomHangMucXe, setListNhomHangMucXe] = useState<Array<CommonExecute.Execute.INhomHangMucXe>>([]); // Danh sách nhóm hạng mục xe cho dropdown

  // ===== KHỞI TẠO DỮ LIỆU BAN ĐẦU =====
  useEffect(() => {
    initData();
  }, []);

  /**
   * Hàm này load danh sách nhóm hạng mục xe để hiển thị trong dropdown filter
   * @param nghiepVu - Mã nghiệp vụ để lọc nhóm (optional)
   */
  const getListNhomHangMucXe = useCallback(async (nghiepVu?: string) => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ma: "",
        ten: "",
        nv: nghiepVu || "",
        trang_thai: "",
        trang: 1,
        so_dong: 100,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_NHOM_HANG_MUC_XE,
      } as any);

      if (response.data) {
        if (Array.isArray(response.data)) {
          setListNhomHangMucXe(response.data); // Response trực tiếp là array
        } else if (response.data && typeof response.data === "object") {
          const responseData = response.data as any;
          if (responseData.data && Array.isArray(responseData.data)) {
            setListNhomHangMucXe(responseData.data); // Response có structure data.data[]
          } else {
            setListNhomHangMucXe([]);
          }
        }
      }
    } catch (error) {
      console.log("getListNhomHangMucXe error ", error);
      setListNhomHangMucXe([]); // Set empty array nếu có lỗi
    }
  }, [mutateUseCommonExecute]);

  /**
   *LẤY CHI TIẾT HẠNG MỤC XE KHI CLICK VÀO ROW
   */
  const getChiTietHangMucXe = useCallback(
    async (data: {ma: string; nv: string}) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.ma,
          nv: data.nv,
          actionCode: ACTION_CODE.CHI_TIET_HANG_MUC_XE,
        } as any);

        const responseData = response.data as any;
        if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          return responseData.lke[0] as CommonExecute.Execute.IHangMucXe;
        }

        return {} as CommonExecute.Execute.IHangMucXe;
      } catch (error) {
        console.log("getChiTietHangMucXe err", error);
        message.error("Không thể tải chi tiết hạng mục xe");
        return {} as CommonExecute.Execute.IHangMucXe;
      }
    },
    [mutateUseCommonExecute],
  );

  /**
   *TÌM KIẾM VÀ LOAD DANH SÁCH HẠNG MỤC XE THEO FILTER PARAMETERS
   * Hàm này được gọi khi filter params thay đổi hoặc cần refresh data
   */
  const searchHangMucXe = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams, // Spread filter params vào request
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_HANG_MUC_XE,
      } as any);

      console.log("[HangMucXe - searchHangMucXe] Raw response:", JSON.stringify(response, null, 2));

      // ===== XỬ LÝ RESPONSE - PATTERN CHO "DANH MỤC" APIs: data.data[] + data.tong_so_dong =====
      const responseData = response.data;
      if (responseData?.data && Array.isArray(responseData.data)) {
        console.log("[HangMucXe] Data check:", {
          "First item": responseData.data[0],
          "Sample items": responseData.data.slice(0, 3).map((item: any) => ({
            ma: item.ma,
            ten: item.ten,
            nv: item.nv,
            loai: item.loai,
            nhom: item.nhom,
            vi_tri: item.vi_tri,
          })),
        });

        setListHangMucXe(responseData.data);
        setTongSoDong(responseData.tong_so_dong || responseData.data.length);
      }
    } catch (error) {
      console.log("searchHangMucXe error ", error);
      message.error("Không thể tải danh sách hạng mục xe");
    }
  }, [mutateUseCommonExecute, filterParams]);

  // ===== AUTO RELOAD KHI FILTER PARAMS THAY ĐỔI =====
  useEffect(() => {
    searchHangMucXe();
  }, [filterParams]);

  /**
   *TẠO MỚI HOẶC CẬP NHẬT THÔNG TIN HẠNG MỤC XE
   */
  const capNhatChiTietHangMucXe = useCallback(
    async (data: ReactQuery.ICapNhatHangMucXeParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_HANG_MUC_XE,
        } as any);

        message.success(data.ma ? "Cập nhật hạng mục xe thành công" : "Thêm mới hạng mục xe thành công");
        return response.data;
      } catch (error) {
        console.log("capNhatChiTietHangMucXe err", error);
        message.error(data.ma ? "Có lỗi xảy ra khi cập nhật hạng mục xe" : "Có lỗi xảy ra khi thêm mới hạng mục xe");
        return {} as CommonExecute.Execute.IHangMucXe;
      }
    },
    [mutateUseCommonExecute],
  );

  /**
   * Hàm này chỉ được gọi 1 lần trong useEffect, không cần memoization
   */
  const initData = () => {
    getListNhomHangMucXe();
  };

  // ===== CONTEXT VALUE - TẠO CONTEXT VALUE VỚI MEMOIZATION ĐỂ TỐI ƯU PERFORMANCE =====
  const value = useMemo<IHangMucXeProvider>(
    () => ({
      // State values
      listNhomHangMucXe,
      listHangMucXe,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading, // Loading state từ API hook
      filterParams,
      // API functions
      searchHangMucXe,
      getChiTietHangMucXe,
      capNhatChiTietHangMucXe,
      getListNhomHangMucXe,
      // State setters
      setFilterParams,
    }),
    [listHangMucXe, listNhomHangMucXe, tongSoDong, mutateUseCommonExecute, searchHangMucXe, getChiTietHangMucXe, capNhatChiTietHangMucXe, getListNhomHangMucXe, filterParams],
  );

  return <HangMucXeContext.Provider value={value}>{children}</HangMucXeContext.Provider>;
};

export default HangMucXeProvider;

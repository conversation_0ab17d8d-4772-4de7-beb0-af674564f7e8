import {SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, Modal, Popconfirm, Table} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {defaultFormValue} from "../index.configs";
import {useChuongTrinhBaoHiemContext} from "../index.context";
import "../index.default.scss";
import {FormTimKiemGoiBaoHiem, IModalThemGoiBaoHiemRef, TableThemGoiBaoHiemDataType, themGoiBaoHiemColumns, ThemgoiProps} from "./index.configs";

const {nd_tim} = FormTimKiemGoiBaoHiem;
const PAGE_SIZE = 6;
const ModalThemGoiBaoHiemComponent = forwardRef<IModalThemGoiBaoHiemRef, ThemgoiProps>(({chiTietChuongTrinhBaoHiem, onDataChange}: ThemgoiProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsOpen(true);
      initData();
    },
    close: () => setIsOpen(false),
  }));

  const initData = async () => {
    try {
      await onSearchApi(defaultFormValue);
    } catch (error) {
      console.log("error", error);
    }
  };
  const {listGoiBaoHiem, loading, danhSachGoiBaoHiemPhanTrang, layDanhSachGoiBaoHiemPhanTrang, onUpdateGoiTrongCTBH, getListGoiBaoHiemTheoCTBH} = useChuongTrinhBaoHiemContext();
  // const [goiBaoHiemSelected, setGoiBaoHiemSelected] = useState<CommonExecute.Execute.IGoiBaoHiemConNguoi | null>(null);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [isOpen, setIsOpen] = useState(false);
  const [dataSource, setDataSource] = useState<TableThemGoiBaoHiemDataType[]>([]);
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams>(defaultFormValue);
  // Khởi tạo hoặc cập nhật dataSource
  useEffect(() => {
    if (danhSachGoiBaoHiemPhanTrang.length > 0) {
      const goiDaChonKeys = new Set(Array.isArray(listGoiBaoHiem) ? listGoiBaoHiem.map(x => `${x.ma_doi_tac_ql}_${x.ma_goi_bh}`) : []);
      // Lọc các gói có ma_doi_tac_ql khớp với chiTietChuongTrinhBaoHiem.ma_doi_tac_ql
      const filteredData = danhSachGoiBaoHiemPhanTrang.filter(item => item.ma_doi_tac_ql === chiTietChuongTrinhBaoHiem?.ma_doi_tac_ql && item.nv === chiTietChuongTrinhBaoHiem?.nv);
      setTongSoDong(filteredData.length);
      const tableData = filteredData.map(item => {
        const uniqueKey = `${item.ma_doi_tac_ql}_${item.ma}`;
        return {
          key: uniqueKey,
          chon: goiDaChonKeys.has(uniqueKey) ? "C" : "K",
          ma: item.ma,
          ten: item.ten,
          ten_sp: item.ten_sp,
          ma_sp: item.ma_sp,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          nv: item.nv,
          id: item.id,
        };
      });

      // Gán key cho hàng trống
      const arrEmptyRow = fillRowTableEmpty(tableData.length, 6).map((row, index) => ({
        ...row,
        key: `empty_${index}`,
      }));

      // console.log("dataSource", [...tableData, ...arrEmptyRow]);
      setDataSource([...tableData, ...arrEmptyRow]);
    } else {
      setDataSource([]);
    }
  }, [danhSachGoiBaoHiemPhanTrang, listGoiBaoHiem]);

  // Xử lý thay đổi checkbox
  const handleInputChange = (key: string, field: keyof TableThemGoiBaoHiemDataType, value: any) => {
    console.log("Updating key:", key, "field:", field, "value:", value);
    if (!key) {
      console.warn("Key is undefined, skipping update");
      return;
    }

    setDataSource(prevDataSource => {
      const updated = prevDataSource.map(item => {
        if (item.key === key) {
          return {...item, [field]: value};
        }
        return item;
      });

      // Cập nhật dữ liệu cho component cha
      const filtered = updated.filter(i => i.chon === "C");
      console.log("Filtered selected rows:", filtered);
      onDataChange?.(filtered);
      return updated;
    });
  };
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      // setPage(page);

      layDanhSachGoiBaoHiemPhanTrang({...searchParams, trang: page, so_dong: PAGE_SIZE});
    },
    [searchParams],
  );
  // Xử lý click vào hàng
  const handleRowClick = (record: TableThemGoiBaoHiemDataType) => {
    if (!record.ma || !record.ma_doi_tac_ql || !record.key) {
      console.warn("Invalid record, skipping row click:", record);
      return;
    }
    const newChonValue = record.chon === "C" ? "K" : "C";
    handleInputChange(record.key, "chon", newChonValue);

    // setGoiBaoHiemSelected({
    //   ma: record.ma,
    //   ten: record.ten,
    //   ten_sp: record.ten_sp,
    //   ma_doi_tac_ql: record.ma_doi_tac_ql,
    //   nv: record.nv,
    // });
  };
  const onApply = async () => {
    const {nv, ma_doi_tac_ql, ma, ma_sp} = chiTietChuongTrinhBaoHiem ?? {};
    const params: ReactQuery.ICapNhatGoiCTBHParams = {
      nv: chiTietChuongTrinhBaoHiem?.nv,
      ma_doi_tac_ql: chiTietChuongTrinhBaoHiem?.ma_doi_tac_ql,
      ma_ctbh: chiTietChuongTrinhBaoHiem?.ma,
      ma_sp: chiTietChuongTrinhBaoHiem?.ma_sp,
      goi_bh: dataSource.map(row => ({
        id: row.id,
        chon: row.chon, // 'C' nếu tích, 'K' nếu bỏ
      })),
    };
    try {
      await onUpdateGoiTrongCTBH(params);

      getListGoiBaoHiemTheoCTBH({ma_ctbh: ma, ma_doi_tac_ql: ma_doi_tac_ql, nv: nv});
      setIsOpen(false);
    } catch (error) {
      console.log("onConApply", error);
    }
    console.log("Áp dụng với các hàng đã chọn:", dataSource);
    // Thêm logic áp dụng tại đây, ví dụ: gửi dữ liệu lên server
  };

  // Tìm kiếm API
  const onSearchApi = async (values: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams & ReactQuery.IPhanTrang) => {
    const cleanedValues = {
      ...values,
      ma_doi_tac_ql: values.ma_doi_tac_ql ?? "",
      ma_sp: values.ma_sp ?? "",
      ma: values.ma ?? "",
      ten: values.ten ?? "",
      nd_tim: values.nd_tim ?? "",
      ngay_ad: values.ngay_ad,
      trang_thai: values.trang_thai ?? "",
      trang: values.trang ?? 1,
      so_dong: values.so_dong ?? 6,
    };
    setSearchParams(cleanedValues);
    try {
      const res = await layDanhSachGoiBaoHiemPhanTrang(cleanedValues);
    } catch (error) {
      console.error("Lỗi khi gọi API tìm kiếm:", error);
    }
  };

  const renderTable = () => {
    return (
      <Table<TableThemGoiBaoHiemDataType>
        className="table-them-goi"
        {...defaultTableProps}
        style={{
          cursor: "pointer",
        }}
        loading={loading}
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })}
        // title={renderHeaderTableThemGoiBaoHiem}
        columns={(themGoiBaoHiemColumns || []).map(renderColumn)}
        dataSource={dataSource}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          defaultPageSize: 6,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
        }}
      />
    );
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderFooter = () => {
    return (
      <Form.Item className="float-right">
        <Popconfirm title="Thông báo" onConfirm={onApply} okText="Lưu" description="Bạn có chắc muốn lưu thông tin?" cancelText="Hủy">
          <Button type="primary" className="mr-2 w-40">
            Áp dụng
          </Button>
        </Popconfirm>

        <Button
          type="primary"
          onClick={() => {
            onApply();
            setIsOpen(false);
          }}
          className="w-40">
          Áp dụng và đóng
        </Button>
      </Form.Item>
    );
  };

  const renderHeaderTableThemGoiBaoHiem = () => {
    return (
      <div className="" style={{marginBottom: "16px"}}>
        <Form layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
          <div className="flex w-full flex-wrap items-end gap-1">
            {renderFormInputColum({...nd_tim})}
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="mb-0 w-full">
                Tìm kiếm
              </Button>
            </Form.Item>
          </div>
        </Form>
      </div>
    );
  };

  const renderColumn = (column: any) => {
    // if (!column.ma || !column.ma_doi_tac_ql) return <span></span>;
    if (column.dataIndex === "chon") {
      return {
        ...column,
        render: (_: any, record: TableThemGoiBaoHiemDataType) => {
          const isEmptyRow = !record.ma;
          if (isEmptyRow) {
            return <div style={{height: 22}} />; // Giữ nguyên chiều cao giống checkbox
          }
          return (
            <FormInput
              className="!mb-0"
              component="checkbox"
              checked={record.chon === "C"}
              onChange={e => {
                console.log("record.key", record.key, "checked:", e.target.checked);
                if (!record.key) {
                  console.warn("Record missing key:", record);
                  return;
                }
                handleInputChange(record.key, "chon", e.target.checked ? "C" : "K");
              }}
            />
          );
        },
      };
    }
    return column;
  };

  return (
    <Flex vertical gap="middle" align="flex-start" style={{maxHeight: "70vh"}}>
      <Modal
        maskClosable={false}
        title="Thông tin gói bảo hiểm"
        className="modal-them-goi-bao-hiem"
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={() => setIsOpen(false)}
        width={{
          xs: "50%",
          sm: "50%",
          md: "50%",
          lg: "50%",
          xl: "50%",
          xxl: "50%",
        }}
        // style={{
        //   height: "70vh",
        // }}
        footer={renderFooter()}>
        {renderHeaderTableThemGoiBaoHiem()}
        {renderTable()}
      </Modal>
    </Flex>
  );
});

ModalThemGoiBaoHiemComponent.displayName = "ModalThemGoiBaoHiemComponent";
export const ModalThemGoiBaoHiem = memo(ModalThemGoiBaoHiemComponent, isEqual);

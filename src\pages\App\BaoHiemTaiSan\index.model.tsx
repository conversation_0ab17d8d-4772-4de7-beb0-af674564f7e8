import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface BaoHiemTaiSanContextProps {
  danhSachHopDongBaoHiemTaiSan: Array<CommonExecute.Execute.IBaoHiemTaiSan>;
  danhSachKhachHang: Array<CommonExecute.Execute.IKhachHang>;
  loading: boolean;
  tongSoDong: number;
  defaultFormValue: object;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  listPhongBan: Array<CommonExecute.Execute.IPhongBan>;
  listPhuongThucKhaiThac: Array<CommonExecute.Execute.IPhuongThucKhaiThac>;
  listChuongTrinhBaoHiem: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
  listDonViBoiThuong: Array<CommonExecute.Execute.INhaBaoHiemTPA>;
  filterParams: ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams & ReactQuery.IPhanTrang;
  tongSoDongCanBoQuanLy: number;
  tongSoDongDataLDaiLy: number;
  tongSoDongDataKhachHang: number;
  listCanBo: Array<CommonExecute.Execute.IDoiTacNhanVien>;
  // listDaiLyKhaiThac: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  // listKhachHang: Array<CommonExecute.Execute.IKhachHang>;
  danhSachDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  chiTietHopDongBaoHiemTaiSan: CommonExecute.Execute.IChiTietHopDongTaiSan | null;
  danhSachDoiTuongBaoHiemTaiSan: Array<CommonExecute.Execute.IBaoHiemTaiSan>;
  tongSoDongDoiTuongBaoHiemTaiSan: number;
  tongPhiBaoHiemTaiSanFromAPI: number;
  listNhomDoiTuong: Array<CommonExecute.Execute.INhomDoiTuong>;
  chiTietDoiTuongBaoHiemTaiSan: CommonExecute.Execute.IDoiTuongBaoHiemTaiSan | null;
  listTinhThanh: Array<CommonExecute.Execute.IDanhMucTinhThanh>;
  listPhuongXa: Array<CommonExecute.Execute.IDanhMucPhuongXa>;
  setListPhuongXa: React.Dispatch<React.SetStateAction<Array<CommonExecute.Execute.IDanhMucPhuongXa>>>;
  getListPhuongXa: (params: ReactQuery.ILietKeDanhSachPhuongXaParams) => Promise<void>;
  layChiTietDoiTuongBaoHiemTaiSan: (params: ReactQuery.IChiTietDoiTuongBaoHiemTaiSanParams) => Promise<CommonExecute.Execute.IDoiTuongBaoHiemTaiSan | null>;
  updateDoiTuongBaoHiemTaiSan: (params: ReactQuery.ICapNhatDoiTuongBaoHiemTaiSanParams) => Promise<boolean>;
  timKiemPhanTrangDoiTuongBaoHiemTaiSan: (params: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams) => void;
  timKiemPhanTrangKhachHang: (params: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => void;
  timKiemPhanTrangDaiLy: (params: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams) => void;
  getListCanBoQuanLy: (params: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams & ReactQuery.IPhanTrang) => void;
  getListDaiLyKhaiThac: (params: ReactQuery.ILietKeDanhSachDaiLyParams) => Promise<{data: Array<CommonExecute.Execute.IDanhMucDaiLy>}>;
  layChiTietHopDongBaoHiemTaiSan: (params: ReactQuery.IChiTietHopDongXeParams) => Promise<CommonExecute.Execute.IChiTietHopDongTaiSan | null>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams & ReactQuery.IPhanTrang>>;
  timKiemPhanTrangHopDongBaoHiemTaiSan: (params: ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams) => void;
  updateHopDongBaoHiemTaiSan: (params: ReactQuery.IUpdateHopDongParams) => Promise<{
    success: boolean;
    isNewContract: boolean;
    contractInfo?: {
      so_id: number;
      so_hd: string;
    };
  }>;
  resetChiTietHopDongBaoHiemTaiSan: () => void;
  huyHopDongTaiSan: () => Promise<boolean>;
  goHuyHopDongTaiSan: () => Promise<boolean>;
  taoHopDongSuaDoiBoSung: (params: ReactQuery.ITaoHopDongSuaDoiBoSungParams) => Promise<{so_id: number; so_hd: string} | null>;
}

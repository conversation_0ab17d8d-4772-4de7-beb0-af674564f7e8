import {IFormInput} from "@src/@types";
import {ruleInputMessage} from "@src/hooks";

export interface ChiTietDoiTacProps {
  danhSachHopDongTrinhDuyetConNguoi: Array<CommonExecute.Execute.IDoiTac>;
}

//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalChiTietThongTinTrinhDuyetRef {
  open: (data?: CommonExecute.Execute.IDoiTac) => void;
  close: () => void;
}

export interface IFormPheDuyetHopDongFieldsConfig {
  nd: IFormInput;
}
export const FormPheDuyetHopDongField: IFormPheDuyetHopDongFieldsConfig = {
  nd: {
    component: "textarea",
    label: "Nội dung",
    name: "nd",
    placeholder: "Nhập nội dung",
    rules: [ruleInputMessage.required],
  },
};

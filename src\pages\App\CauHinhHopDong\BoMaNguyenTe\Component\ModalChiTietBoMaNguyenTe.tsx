import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {Button, Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useChiTietBoMaNguyenTeContext} from "../index.context";
import {FormChiTietBoMaNguyenTe, TRANG_THAI_TAO_MOI_DOI_TAC} from "./index.configs";

const {ma_doi_tac_ql, ma, ten, trang_thai, stt} = FormChiTietBoMaNguyenTe;
interface Props {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
}

//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalChiTietBoMaNguyenTeRef {
  open: (data?: CommonExecute.Execute.IChiTietBoMaNguyenTe) => void;
}

const ModalChiTietBoMaNguyenTeComponent = forwardRef<IModalChiTietBoMaNguyenTeRef, Props>(({listDoiTac}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDoiTac?: CommonExecute.Execute.IChiTietBoMaNguyenTe) => {
      setIsOpen(true);
      if (dataDoiTac) setChiTietBoMaNguyenTe(dataDoiTac);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietBoMaNguyenTe, setChiTietBoMaNguyenTe] = useState<CommonExecute.Execute.IChiTietBoMaNguyenTe | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {loading, updateBoMaNguyenTe} = useChiTietBoMaNguyenTeContext();

  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  //chuyển đổi listđối tác thành định dạng option

  const handleChangeDoitac = (ma_doi_tac_ql: string) => {
    console.log("Đã chọn đối tác", ma_doi_tac_ql);
  };
  // init form data// ĐỔ DỮ LIỆU VÀO FORM
  useEffect(() => {
    if (chiTietBoMaNguyenTe) {
      console.log("chiTietBoManguyenTe", chiTietBoMaNguyenTe);
      const arrFormData = [];
      for (const key in chiTietBoMaNguyenTe) {
        arrFormData.push({
          name: key,
          value: chiTietBoMaNguyenTe[key as keyof CommonExecute.Execute.IChiTietBoMaNguyenTe],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietBoMaNguyenTe]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietBoMaNguyenTe(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateBoMaNguyenTeParams = form.getFieldsValue(); //lấy ra values của form
      //với trường hợp tạo mới -> check mã đối tác đã tồn tại
      console.log("values", values);

      // if (!chiTietBoMaNguyenTe) {
      //   for (let i = 0; i < listDoiTac.length; i++) {
      //     if (listDoiTac[i].ma === values.ma) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã đối tác đã tổn tại!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }
      await updateBoMaNguyenTe(values); //cập nhật lại đối tác

      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //get màu trạng thái sử dụng
  const getStatusColor = (status?: string) => {
    let color = COLOR_PALETTE.gray[70];
    if (status === "D") color = COLOR_PALETTE.green[100];
    else if (status === "K") color = COLOR_PALETTE.red[50];
    return color;
  };

  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: any, span?: number) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI_TAO_MOI_DOI_TAC[0]}}>
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormColum({...ma_doi_tac_ql, disabled: chiTietBoMaNguyenTe ? true : false, options: listDoiTac}, 16)}
        {renderFormColum({...ma, disabled: chiTietBoMaNguyenTe ? true : false}, 8)}
      </Row>

      {/* gutter : khoảng cách giữa các ô */}
      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormColum({...ten}, 8)}
        {renderFormColum({...stt}, 8)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DOI_TAC}, 8)}
      </Row>

      <Row gutter={16}>{/* 3 cột có width = nhau  */}</Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        styles={{
          body: {
            height: "20vh",
          },
        }}
        title={
          <HeaderModal
            title={chiTietBoMaNguyenTe ? `Chi tiết nguyên tệ ${chiTietBoMaNguyenTe.ten}` : "tạo mới nguyên tệ"}
            trang_thai_ten={chiTietBoMaNguyenTe?.trang_thai_ten}
            trang_thai={chiTietBoMaNguyenTe?.trang_thai}
          />
        }
        // centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "50%",
          sm: "50%",
          md: "50%",
          lg: "50%",
          xl: "50%",
          xxl: "50%",
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietBoMaNguyenTeComponent.displayName = "ModalChiTietBoMaNguyenTeComponent";
export const ModalChiTietBoMaNguyenTe = memo(ModalChiTietBoMaNguyenTeComponent, isEqual);

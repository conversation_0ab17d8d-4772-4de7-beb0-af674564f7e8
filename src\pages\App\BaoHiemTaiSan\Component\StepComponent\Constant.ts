import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {formatCurrencyUS, parseDateTime} from "@src/utils";
import {TableProps, Tag} from "antd";
import React from "react";

export const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};
export const defaultFormValueTimKiemDoiTuongBaoHiemTaiSan: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams = {
  so_id: 0,
  // gcn: "",
  // ten: "",
  nd_tim: "",
  trang: 1,
  so_dong: 14, // set khác default cho vừa màn hình
  ma_dvi_dong_tai: "",
  dong_tai: "",
};

//INTERFACE ĐỐI TƯỢNG
export interface TableDoiTuongColumnDataType {
  key: string;
  sott?: number; //CỘT 1
  ten?: string; //CỘT 2
  so_id?: number | string;
  so_id_dt?: number | string;
  tong_phi?: number | string;
  sdbs?: string; // Trạng thái đối tượng: N-Mới, S-Sửa đổi, K-Không sửa đổi, H-Kết thúc hiệu lực
  ap_dung: string;
  tl_dong?: string | number;
  tl_tai?: string | number;
  ma_dvi_dong_tai: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDoiTuongColumn: TableProps<TableDoiTuongColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "Tên đối tượng",
    dataIndex: "ten",
    key: "ten",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Phí BH",
    dataIndex: "tong_phi",
    key: "tong_phi",
    width: "25%",
    align: "right",
    ellipsis: true,
    render: (text: any) => {
      if (typeof text === "number") {
        return formatCurrencyUS(text);
      }
    },
  },
];

//FORM TÌM KIẾM ĐỐI TƯỢNG
//INTERFACE FORM TÌM KIẾM ĐỐI TƯỢNG
export interface IFormTimKiemPhanTrangDoiTuongBaoHiemTaiSanFieldsConfig {
  so_id?: IFormInput;
  gcn?: IFormInput;
  ten?: IFormInput;
  nd_tim?: IFormInput;
  dong_tai?: IFormInput;
}
// KHƠI TẠO FORM TÌM KIẾM ĐỐI TƯỢNG
export const FormTimKiemDoiTuongBaoHiemTaiSan: IFormTimKiemPhanTrangDoiTuongBaoHiemTaiSanFieldsConfig = {
  nd_tim: {
    component: "input",
    // label: "Số GCN/BSX/số khung/số máy",
    name: "ten",
    placeholder: "Tên đối tượng",
  },
};
//FORM THÊM /SỬA ĐỐI TƯỢNG
//INTERFACE FORM THÊM /SỬA ĐỐI TƯỢNG
export interface IFormDoiTuongBaoHiemTaiSan {
  so_id: IFormInput;
  so_id_dt: IFormInput;
  ten: IFormInput;
  gcn: IFormInput;
  nhom_dt: IFormInput;
  latitude: IFormInput;
  longitude: IFormInput;
  gia_tri: IFormInput;
  gio_hl: IFormInput;
  ngay_hl: IFormInput;
  gio_kt: IFormInput;
  ngay_kt: IFormInput;
  ngay_cap: IFormInput;
  vip: IFormInput;
  dk: IFormInput;
  dkbs: IFormInput;
  vi_tri: IFormInput;
  tinh_thanh: IFormInput;
  phuong_xa: IFormInput;
  dchi: IFormInput;
  ttinh: IFormInput;
}

//KHỞI TẠO FORM THÊM /SỬA ĐỐI TƯỢNG
export const FormTaoMoiDoiTuongBaoHiemTaiSan: IFormDoiTuongBaoHiemTaiSan = {
  so_id: {
    // component: "input",
    // label: "Số ID",
    name: "so_id",
    // placeholder: "Nhập số ID",
    // rules: [ruleRequired],
    className: "hidden",
  },
  so_id_dt: {
    // component: "input",
    // label: "Số ID đối tác",
    name: "so_id_dt",
    // placeholder: "Nhập số ID đối tác",
    // rules: [ruleRequired],
    className: "hidden",
  },
  ten: {
    component: "input",
    label: "Tên đối tượng",
    name: "ten",
    placeholder: "Tên đối tượng",
    rules: [ruleRequired],
  },
  gcn: {
    component: "input",
    label: "Giấy chứng nhận",
    name: "gcn",
    placeholder: "Nhập giấy chứng nhận",
    rules: [ruleRequired],
    normalize: (value: string) => value?.toUpperCase(),
  },
  gia_tri: {
    component: "input-price",
    label: "Giá trị",
    name: "gia_tri",
    placeholder: "0",
    rules: [ruleRequired],
  },
  nhom_dt: {
    component: "select",
    label: "Nhóm đối tượng",
    name: "nhom_dt",
    placeholder: "Chọn nhóm đối tượng",
    rules: [ruleRequired],
  },
  latitude: {
    component: "input",
    label: "Kinh độ",
    name: "latitude",
    placeholder: "Nhập kinh độ",
    rules: [ruleRequired],
  },
  longitude: {
    component: "input",
    label: "Vĩ độ",
    name: "longitude",
    placeholder: "Nhập vĩ độ",
    rules: [ruleRequired],
  },
  //   gia_tri: {
  //     component: "input-price",
  //     label: "Giá trị",
  //     name: "gia_tri",
  //     placeholder: "0",
  //     rules: [ruleRequired],
  //   },
  gio_hl: {
    component: "time-picker",
    label: "Giờ hiệu lực",
    name: "gio_hl",
    placeholder: "Chọn giờ",
    className: "w-full",
    rules: [ruleRequired],
  },
  ngay_hl: {
    component: "date-picker",
    label: "Ngày hiệu lực",
    name: "ngay_hl",
    placeholder: "Chọn ngày",
    className: "w-full",
    rules: [ruleRequired],
  },
  gio_kt: {
    component: "time-picker",
    label: "Giờ kết thúc",
    name: "gio_kt",
    className: "w-full",
    placeholder: "Chọn giờ",
    rules: [ruleRequired],
  },
  ngay_kt: {
    component: "date-picker",
    label: "Ngày kết thúc",
    name: "ngay_kt",
    className: "w-full",
    placeholder: "Chọn ngày",
    rules: [ruleRequired],
  },
  ngay_cap: {
    component: "date-picker",
    label: "Ngày cấp",
    name: "ngay_cap",
    placeholder: "Chọn ngày",
    className: "w-full",
    rules: [ruleRequired],
  },
  vi_tri: {
    component: "select",
    label: "Vị trí",
    name: "vi_tri",
    placeholder: "Nhập vị trí",
    rules: [ruleRequired],
  },
  tinh_thanh: {
    component: "select",
    label: "Tỉnh thành",
    name: "tinh_thanh",
    placeholder: "Chọn tỉnh thành",
    // rules: [ruleRequired],
  },
  phuong_xa: {
    component: "select",
    label: "Phường xã",
    name: "phuong_xa",
    placeholder: "Chọn phường xã",
    // rules: [ruleRequired],
  },
  dchi: {
    component: "input",
    label: "Địa chỉ",
    name: "dchi",
    placeholder: "Nhập địa chỉ",
    // rules: [ruleRequired],
  },
  vip: {
    component: "select",
    label: "Hợp đồng VIP",
    name: "vip",
    placeholder: "Chọn hợp đồng VIP",
  },
  dk: {
    name: "dk",
    className: "hidden",
  },
  dkbs: {
    name: "dkbs",
    className: "hidden",
  },
  ttinh: {
    name: "ttinh",
    className: "hidden",
  },
};
//option select hợp đồng VIP
export const listHDVipSelect = [
  {ma: "VIP", ten: "VIP"},
  {ma: "", ten: "Không"},
];
//khởi tạo form chi tiết đối tượng
export const initFormFieldsDoiTuong = (form: any, chiTietDoiTuongBaoHiemTaiSan: any) => {
  if (!chiTietDoiTuongBaoHiemTaiSan) return;
  const {gcn, gcn_dk, gcn_dkbs, gcn_ct} = chiTietDoiTuongBaoHiemTaiSan;

  const DATE_TIME_FIELDS = ["ngay_cap", "ngay_hl", "ngay_kt", "gio_hl", "gio_kt"];

  const fields = Object.entries(gcn || {}).map(([name, value]) => {
    if (!value) return {name: "", value: ""};
    //formart ngày
    if (DATE_TIME_FIELDS.includes(name)) {
      return {
        name,
        value: parseDateTime(value),
        errors: [],
      };
    }

    return {name, value, errors: []};
  });

  // Set fields from gcn
  form.setFields(fields);

  // Set fields from gcn_dk
  if (gcn_dk) {
    form.setFieldsValue({dk: gcn_dk});
    form.setFields([{name: "dk", touched: true}]);
  }
  if (gcn_ct && Array.isArray(gcn_ct)) {
    const ts_ct_data = gcn_ct.map(item => ({
      ma_thuoc_tinh: item.ma_thuoc_tinh,
      ten_thuoc_tinh: item.ten_thuoc_tinh,
      gia_tri: item.gia_tri,
      kieu_dl: item.kieu_dl,
    }));
    form.setFieldValue("gcn_ct", ts_ct_data);
  }
  // Set fields from gcn_dkbs
  if (gcn_dkbs) {
    form.setFieldsValue({dkbs: gcn_dkbs});
    form.setFields([{name: "dkbs", touched: true}]);
  }
};
export const VI_TRI = [
  {ma: "C", ten: "Cố định"},
  {ma: "K", ten: "Không cố định"},
];
export interface TableDoiTuongCTDataType {
  key: string;
  stt: number;
  gia_tri: string;
  ma_thuoc_tinh: string;
  ten_thuoc_tinh: string;
  kieu_dl: string;
  // hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const doiTuongCTColumns: TableProps<TableDoiTuongCTDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã thuộc tính", dataIndex: "ma_thuoc_tinh", key: "ma_thuoc_tinh", width: 150, className: "col-ma-thuoc-tinh", align: "center", ...defaultTableColumnsProps},
  {title: "Tên thuộc tính", dataIndex: "ten_thuoc_tinh", key: "ten_thuoc_tinh", width: 230, align: "left", ...defaultTableColumnsProps},
  {title: "Kiểu dữ liệu", dataIndex: "kieu_dl", key: "kieu_dl", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Giá trị thuộc tính", dataIndex: "gia_tri", key: "gia_tri", align: "center", ...defaultTableColumnsProps},
  // {
  //   title: "Xóa",
  //   dataIndex: "hanh_dong",
  //   // key: "hanh_dong",
  //   width: 100,
  //   render: (_, record) => record.hanh_dong?.(),
  //   ...defaultTableColumnsProps,
  // },
];
export type DataIndexDoiTuongCT = keyof TableDoiTuongCTDataType;
//KIEU_DU_LIEU DATE, NUMBER, TEXT
export const KIEU_DU_LIEU = [
  {ten: "DATE", ma: "DATE"},
  {ten: "NUMBER", ma: "NUMBER"},
  {ten: "TEXT", ma: "TEXT"},
];

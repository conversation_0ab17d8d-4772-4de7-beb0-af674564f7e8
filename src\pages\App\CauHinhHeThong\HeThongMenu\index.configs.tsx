import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface TableHeThongMenuDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ma_cha?: string;
  url?: string;
  nhom?: string;
  sott?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const heThongMenuColumns: TableProps<TableHeThongMenuDataType>["columns"] = [
  {title: "STT", dataIndex: "sott", key: "sott", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 150, align: "center", ...defaultTableColumnsProps},
  {...defaultTableColumnsProps, title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left"},
  {title: "Mã cha", dataIndex: "ma_cha", key: "ma_cha", width: 130, align: "center", ...defaultTableColumnsProps},
  {title: "Url", dataIndex: "url", key: "url", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Nhóm menu", dataIndex: "nhom", key: "nhom", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Thứ tự", dataIndex: "stt", key: "stt", width: 80, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];
//Form
export interface IFormTimKiemHeThongMenuFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  nhom: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemHeThongMenu: IFormTimKiemHeThongMenuFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã menu",
    placeholder: "Mã menu",
    // width: "10%",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên menu",
    placeholder: "Tên menu",
    // width: "20%",
  },
  nhom: {
    component: "select",
    name: "nhom",
    label: "Nhóm menu",
    placeholder: "Chọn nhóm menu",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    // width: "20%",
  },
};
// export const defaultFormValue: ReactQuery.ITimkiemPhanTrangHeThongMenuParams = {
//   ma: "",
//   ten: "",
//   nhom: "",
//   trang_thai: "",
//   trang: 1,
//   so_dong: 13,
//   actionCode: "",
// };

export const TRANG_THAI_MENU = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export const NHOM_MENU = [
  {ten: "CLIENT", ma: "CLIENT"},
  {ten: "ADMIN", ma: "ADMIN"},
];

export const radioItemTrangThaiMenuTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];

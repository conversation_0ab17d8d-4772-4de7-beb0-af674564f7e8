import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {CauHinhTyLeHoaHongContext} from "./index.context";
import {CauHinhTyLeHoaHongContextProps} from "./index.model";
import {message} from "antd";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/

const CauHinhTyLeHoaHongProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachDaiLy, setDanhSachDaiLy] = useState<Array<CommonExecute.Execute.IDanhMucDaiLy>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang>({
    ma_doi_tac_ql: "",
    ma: "",
    ten: "",
    loai: "",
    trang_thai: "",
    trang: 1,
    // so_dong: ,
  });
  const [danhSachTyLeHoaHong, setDanhSachTyLeHoaHong] = useState<Array<CommonExecute.Execute.ICauHinhTyLeHoaHong>>([]);
  const [cayTyLeHoaHong, setCayTyLeHoaHong] = useState<Array<CommonExecute.Execute.ICayTyLeHoaHong>>([]);
  useEffect(() => {
    initData();
  }, []);

  const initData = () => {
    layDanhSachDaiLy(filterParams);
    getListDoiTac();
  };
  useEffect(() => {
    layDanhSachDaiLy(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  //DS đại lý phân trang
  const layDanhSachDaiLy = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_DAI_LY,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data || [];
        const tongSoDong = response.data.tong_so_dong || 0;

        // setDanhSachDaiLy(data);
        // setTongSoDong(response.data.tong_so_dong);
        return {
          data,
          tong_so_dong: tongSoDong,
        };
      } catch (error: any) {
        console.log("Lấy danh sách đại lý error:", error.message || error);
        return {
          data: [],
          tong_so_dong: 0,
        };
      }
    },
    [mutateUseCommonExecute],
  );

  //Lấy chi tiết 1 đại lý
  const layChiTietDaiLy = useCallback(
    async (item: ReactQuery.IChiTietDanhMucDaiLyParams): Promise<CommonExecute.Execute.IChiTietDanhMucDaiLy | null> => {
      try {
        const params = {
          ma: item.ma,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          actionCode: ACTION_CODE.GET_CHI_TIET_DAI_LY,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = responseData.data.data;
        // setChiTietDaiLy(data);
        console.log("responseData.data", responseData.data);
        return responseData.data as CommonExecute.Execute.IChiTietDanhMucDaiLy;
      } catch (error: any) {
        console.log("layChiTietdaily error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy danh sách tỷ lệ hoa hồng liệt kê

  const layDanhSachTyLeHoaHong = useCallback(
    async (item: ReactQuery.ILietKeDanhSachTyLeHoaHongParams) => {
      try {
        const params = {
          ...item,
          actionCode: ACTION_CODE.LIET_KE_CAU_HINH_TY_LE_HOA_HONG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = responseData.data;
        setDanhSachTyLeHoaHong(data);
        console.log("responseData.data danh sách tỷ lệ hoa hồng ", data);
        console.log("danh sách tỷ lệ hoa hồng", danhSachTyLeHoaHong);
        return responseData.data as CommonExecute.Execute.ICauHinhTyLeHoaHong;
      } catch (error: any) {
        console.log("lấy danh sách tỷ lệ hoa hồng error ", error.message || error);
        return [];
      }
    },
    [mutateUseCommonExecute],
  );
  const onUpdateCauHinhTyLeHoaHong = useCallback(
    async (body: ReactQuery.ICapNhatCauHinhTyLeHoaHongParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HINH_TY_LE_HOA_HONG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          return true;
        } else return false;
      } catch (error: any) {
        console.log("Cập nhật cấu hình tỷ lệ hoa hồng error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );
  const layCayTyLeHoaHong = useCallback(
    async (item: ReactQuery.ILayCayTyLeHoaHongParams) => {
      try {
        const params = {
          ...item,
          actionCode: ACTION_CODE.LIET_KE_CAY_TY_LE_HOA_HONG,
        };
        const responseData: CommonExecuteResponse = await (mutateUseCommonExecute.mutateAsync as any)(params);
        const data = responseData.data as Array<CommonExecute.Execute.ICayTyLeHoaHong>;
        setCayTyLeHoaHong(data);
        console.log("hiển thị cây", data);
        return data;
      } catch (error: any) {
        console.log("layCayTyLeHoaHong error ", error.message || error);
        return [];
      }
    },
    [mutateUseCommonExecute],
  );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<CauHinhTyLeHoaHongContextProps>(
    () => ({
      listDoiTac,
      tongSoDong,
      danhSachDaiLy,
      loading: mutateUseCommonExecute.isLoading,
      danhSachTyLeHoaHong,
      filterParams,
      cayTyLeHoaHong,
      setCayTyLeHoaHong,
      layCayTyLeHoaHong,
      setDanhSachTyLeHoaHong,
      onUpdateCauHinhTyLeHoaHong,
      setFilterParams,
      getListDoiTac,
      layDanhSachTyLeHoaHong,
      layDanhSachDaiLyPhanTrang: layDanhSachDaiLy,
      layChiTietDaiLy,
    }),
    [
      danhSachDaiLy,
      mutateUseCommonExecute,
      filterParams,
      tongSoDong,
      danhSachTyLeHoaHong,
      listDoiTac,
      cayTyLeHoaHong,
      setCayTyLeHoaHong,
      layCayTyLeHoaHong,
      setDanhSachTyLeHoaHong,
      setFilterParams,
      layDanhSachTyLeHoaHong,
      layDanhSachDaiLy,
      layChiTietDaiLy,
      getListDoiTac,
      onUpdateCauHinhTyLeHoaHong,
    ],
  );

  return <CauHinhTyLeHoaHongContext.Provider value={value}>{children}</CauHinhTyLeHoaHongContext.Provider>;
};

export default CauHinhTyLeHoaHongProvider;

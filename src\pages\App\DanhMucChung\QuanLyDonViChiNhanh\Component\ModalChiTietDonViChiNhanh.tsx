import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, message, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiDonViChiNhanh, TRANG_THAI_TAO_MOI_DON_VI_CHI_NHANH} from "../index.configs";
import {useQuanLyDonViChiNhanhContext} from "../index.context";
import {IModalChiTietDonViChiNhanhRef, ModalChiTietDonViChiNhanhProps} from "./Constant";

const {ma, ten, ten_tat, mst, dchi, dthoai, stt, trang_thai, email, ma_doi_tac} = FormTaoMoiDonViChiNhanh;

const ModalChiTietDonViChiNhanhComponent = forwardRef<IModalChiTietDonViChiNhanhRef, ModalChiTietDonViChiNhanhProps>(({listDonViChiNhanh}: ModalChiTietDonViChiNhanhProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDonViChiNhanh?: CommonExecute.Execute.IChiNhanh) => {
      setIsOpen(true);
      setDisableSubmit(true);
      if (dataDonViChiNhanh) setChiTietDonViChiNhanh(dataDonViChiNhanh);
    },
    close: () => setIsOpen(false),
  }));

  const [chiTietDonViChiNhanh, setChiTietDonViChiNhanh] = useState<CommonExecute.Execute.IChiNhanh | null>(null);

  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDonViChiNhanh, searchDonViChiNhanh, loading, listDoiTac} = useQuanLyDonViChiNhanhContext();

  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);

  // init form data
  useEffect(() => {
    if (chiTietDonViChiNhanh) {
      const arrFormData = [];
      for (const key in chiTietDonViChiNhanh) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IChiNhanh,
          value: chiTietDonViChiNhanh[key as keyof CommonExecute.Execute.IChiNhanh],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDonViChiNhanh]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDonViChiNhanh(null);
    form.resetFields();
  }, [form]);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDonViChiNhanhParams = form.getFieldsValue(); //lấy ra values của form

      const response = await capNhatChiTietDonViChiNhanh(values); //cập nhật lại đơn vị chi nhánh
      if (response === -1) {
        await searchDonViChiNhanh(); //lấy lại danh sách đơn vị chi nhánh
        message.success((!chiTietDonViChiNhanh ? "Tạo mới" : "Cập nhật") + " thành công ");
        closeModal();
      }
    } catch (error) {
      console.log("onConfirm error", error);
    }
  };

  // RENDER

  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle="Lưu"
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{doi_tac: chiTietDonViChiNhanh ? chiTietDonViChiNhanh.ma_doi_tac : ""}}>
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormColum({...ma_doi_tac, disabled: chiTietDonViChiNhanh ? true : false, options: listDoiTac}, 6)}
        {renderFormColum({...ma, disabled: chiTietDonViChiNhanh ? true : false}, 6)}
        {renderFormColum({...ten}, 6)}
        {renderFormColum({...ten_tat}, 6)}
      </Row>

      {/* gutter : khoảng cách giữa các ô */}
      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}

        {renderFormColum({...mst}, 6)}
        {renderFormColum({...dchi}, 18)}
      </Row>

      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormColum({...dthoai}, 6)}
        {renderFormColum({...email}, 6)}
        {renderFormColum({...stt}, 6)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DON_VI_CHI_NHANH}, 6)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDonViChiNhanh ? ` ${chiTietDonViChiNhanh.ten}` : "Tạo mới đơn vị / chi nhánh"}
            trang_thai_ten={chiTietDonViChiNhanh?.trang_thai_ten}
            trang_thai={chiTietDonViChiNhanh?.trang_thai}
          />
        }
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={"70vw"}
        styles={{
          body: {
            height: "32vh",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietDonViChiNhanhComponent.displayName = "ModalChiTietDonViChiNhanhComponent";
export const ModalChiTietDonViChiNhanh = memo(ModalChiTietDonViChiNhanhComponent, isEqual);

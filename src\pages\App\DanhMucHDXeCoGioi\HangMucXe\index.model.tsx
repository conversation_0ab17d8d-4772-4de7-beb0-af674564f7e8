import {ReactQuery} from "@src/@types";

/**
 * Interface này định nghĩa tất cả state và methods mà Provider cung cấp cho các component con
 * thông qua Context API để quản lý danh mục hạng mục xe
 */
export interface IHangMucXeProvider {
  // ===== STATE DATA - DỮ LIỆU TRẠNG THÁI =====
  listHangMucXe: CommonExecute.Execute.IHangMucXe[]; 
  listNhomHangMucXe: CommonExecute.Execute.INhomHangMucXe[]; // Danh sách nhóm hạng mục xe cho dropdown filter
  tongSoDong: number; 
  loading: boolean; 
  
  // ===== FILTER PARAMETERS - THAM SỐ LỌC VÀ PHÂN TRANG =====
  filterParams: ReactQuery.ITimKiemPhanTrangHangMucXeParams & ReactQuery.IPhanTrang; // Tham số tìm kiếm và phân trang
  
  // ===== API METHODS - CÁC PHƯƠNG THỨC GỌI API =====
  searchHangMucXe: () => Promise<void>;
  getChiTietHangMucXe: (data: {ma: string; nv: string}) => Promise<CommonExecute.Execute.IHangMucXe>; // Lấy chi tiết hạng mục xe khi click row
  capNhatChiTietHangMucXe: (data: ReactQuery.ICapNhatHangMucXeParams) => Promise<any>;
  getListNhomHangMucXe: (nghiepVu?: string) => Promise<void>; // Lấy danh sách nhóm hạng mục xe theo nghiệp vụ
  
  // ===== STATE SETTERS - CÁC HÀM CẬP NHẬT STATE =====
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangHangMucXeParams & ReactQuery.IPhanTrang>>; 
}

import {ReactQuery} from "@src/@types";

export interface IHopDongConNguoiContextProps {
  listHopDong: Array<CommonExecute.Execute.IHopDongConNguoi>;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  listChuongTrinhBaoHiem: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
  listPhuongThucKhaiThac: Array<CommonExecute.Execute.IChiTietPhuongThucKhaiThac>;
  listDaiLyKhaiThac: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  listDonViBoiThuongTPA: Array<CommonExecute.Execute.INhaBaoHiemTPA>;
  listCanBo: Array<CommonExecute.Execute.IDoiTacNhanVien>;
  listPhongBan: Array<CommonExecute.Execute.IPhongBan>;
  listNguyenTe: {ma?: string; ten?: string}[];
  windowHeight: number;
  chiTietHopDong: CommonExecute.Execute.IHopDongConNguoi | null;

  loading: boolean;
  filterHopDongParams: ReactQuery.ILayDanhSachHopDongConNguoiPhanTrangParams;
  tongSoDongHopDong: number;

  timKiemPhanTrangNguoiDuocBaoHiemParams: ReactQuery.ITimKiemPhanTrangNguoiDuocBaoHiemHopDongConNguoi;
  listNguoiDuocBaoHiem: CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi[];
  tongSoDongNguoiDuocBaoHiem: number;
  tongPhiBaoHiemFromAPI: number;
  listGoiBaoHiem: CommonExecute.Execute.IGoiBaoHiemConNguoi[];
  chiTietNguoiDuocBaoHiem: CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi | undefined;
  loadingQuyenLoi: boolean;
  listThongTinThanhToanHopDongConNguoi: CommonExecute.Execute.IThongTinThanhToanCuaHopDong[];
  listDongBaoHiemHopDongConNguoi: CommonExecute.Execute.IDongBaoHiem[];
  listDonViDongTai: CommonExecute.Execute.IDonViDongTai[];
  listTaiBaoHiemHopDongConNguoi: CommonExecute.Execute.ITaiBaoHiem[];

  timKiemPhanTrangHopDongBaoHiem: () => Promise<void>;
  getListDoiTac: () => Promise<void>;
  getListChiNhanh: (params: ReactQuery.ILayDanhSachDonViChiNhanhParams) => Promise<void>;
  getListPhongBan: (params: ReactQuery.ILayDanhSachPhongBanPhanTrangParams) => Promise<void>;
  getListCanBoQuanLy: (params: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams) => Promise<{data: Array<CommonExecute.Execute.IDoiTacNhanVien>; tong_so_dong: number}>;
  getListSanPham: (params: ReactQuery.ILietKeSanPhamParams) => Promise<void>;
  getListChuongTrinHBaoHiem: (params: ReactQuery.ILietKeChuongTrinhBaoHiemParams) => Promise<void>;
  getListPhuongThucKhaiThac: (params: ReactQuery.ILietKePhuongThucKhaiThacParams) => Promise<void>;
  getListDaiLyKhaiThac: (params: ReactQuery.ILietKeDanhSachDaiLyParams) => Promise<void>;
  getListDonViBoiThuong: (params: ReactQuery.ILietKeDonViBoiThuongTPAParams) => Promise<void>;
  searchKhachHang: (params: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => Promise<{tong_so_dong: number; data: Array<CommonExecute.Execute.IKhachHang>}>;
  searchDaiLy: (params: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams) => Promise<{tong_so_dong: number; data: Array<CommonExecute.Execute.IDanhMucDaiLy>}>;
  getChiTietHopDong: (params: ReactQuery.IChiTietHopDongConNguoiParams) => Promise<CommonExecute.Execute.IHopDongConNguoi>;
  setFilterHopDongParams: React.Dispatch<React.SetStateAction<ReactQuery.ILayDanhSachHopDongConNguoiPhanTrangParams>>;
  updateHopDong: (params: ReactQuery.IUpdateHopDongParams) => Promise<{
    data?: number;
    output?: {
      so_hd?: string;
      so_id?: number;
    };
  }>;

  timKiemPhanTrangNguoiDuocBaoHiem: () => void;
  getChiTietNguoiDuocBaoHiem: (params: ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi) => void;
  luuThongTinNguoiDuocBaoHiem: (params: ReactQuery.ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi) => Promise<any>;
  setTimKiemPhanTrangNguoiDuocBaoHiemParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangNguoiDuocBaoHiemHopDongConNguoi>>;
  timKiemPhanTrangGoiBaoHiem: (params: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams) => Promise<any>;
  setChiTietNguoiDuocBaoHiem: React.Dispatch<React.SetStateAction<CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi | undefined>>;
  lietKeDieuKhoanNguoiDuocBaoHiem: (params: ReactQuery.ILietKeDieuKhoanNguoiDuocBaoHiemParams) => Promise<any>;
  luuDieuKhoanNguoiDuocBaoHiem: (params: ReactQuery.ILuuDieuKhoanNguoiDuocBaoHiemParams) => Promise<any>;
  lietKeDieuKhoanBoSungNguoiDuocBaoHiem: (params: ReactQuery.ILietKeDieuKhoanBoSungNguoiDuocBaoHiemParams) => Promise<any>;
  luuDieuKhoanBoSungNguoiDuocBaoHiem: (params: ReactQuery.ILuuDieuKhoanBoSungNguoiDuocBaoHiemParams) => Promise<any>;
  getListNguoiPhuThuoc: (params: ReactQuery.ILietKeDanhSachNguoiPhuThuocHopDongConNguoiParams) => Promise<any>;
  getChiTietNguoiPhuThuoc: (params: ReactQuery.IChiTietNguoiPhuThuocHopDongConNguoiParams) => Promise<any>;
  capNhatNguoiPhuThuoc: (params: ReactQuery.ICapNhatNguoiPhuThuocHopDongConNguoiParams) => Promise<any>;
  getDanhSachFileThumbnailTheoDoiTuong: (params: ReactQuery.IGetFileThumbnailParams) => Promise<any>;
  uploadFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise<any>;
  deleteFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise<any>;
  layThongTinThanhToanCuaHopDongBaoHiemConNguoi: (params: ReactQuery.ILayThongTinThanhToanCuaHopDongBaoHiemParams) => Promise<any>;
  updateKyThanhToan: (params: ReactQuery.IUpdateKyThanhToanParams) => Promise<any>;
  layChiTietKyThanhToan: (params: ReactQuery.IChiTietKyThanhToanParams) => Promise<any>;
  layChiTietThongTinCauHinhDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<any>;
  xoaThongTinDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<any>;
  lietKeDanhSachCacDoiTuongDaDuocApDungDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<any>;
  updateCauHinhDongBaoHiem: (params: ReactQuery.IUpdateCauHinhDongBHParams) => Promise<any>;
  layDanhSachCauHinhDongCuaHopDongBaoHiemConNguoi: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise<any>;
  layDanhSachCauHinhTaiBHCuaHopDongBaoHiemConNguoi: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise<any>;
  layChiTietThongTinCauHinhTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<any>;
  updateCauHinhTaiBaoHiem: (params: ReactQuery.IUpdateCauHinhTaiBHParams) => Promise<boolean>;
  xoaThongTinTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<any>;
  updateDoiTuongApDungTaiBH: (params: ReactQuery.IUpdateDoiTuongApDungTaiBHParams) => Promise<boolean>;
  lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<any>;
  exportPdfHopDong: (params: ReactQuery.IExportPDFHopDongParams) => Promise<string | null>;
  updateDoiTuongApDungTyLeDongBH: (params: ReactQuery.IUpdateDoiTuongApDungDongBHParams) => Promise<boolean>;
  layDanhSachCauHoiDanhGiaSucKhoe: (params: ReactQuery.ILayDanhSachCauHoiDanhGiaSucKhoeParams) => Promise<{cau_hoi: any[]; gia_tri: any[]}>;
  luuDanhGiaSucKhoe: (params: ReactQuery.ILuuDanhGiaSucKhoeParams) => Promise<boolean>;

  // Bệnh viện API functions cho Hợp đồng con người
  timKiemBenhVienPhanTrangHopDongConNguoi: (params: ReactQuery.ITimKiemBenhVienHopDongConNguoiParams) => Promise<any>;
  layDanhSachBenhVienDaLuuHopDongConNguoi: (params: ReactQuery.ILayDanhSachBenhVienDaLuuHopDongConNguoiParams) => Promise<any>;
  luuCauHinhBenhVienHopDongConNguoi: (params: ReactQuery.ILuuCauHinhBenhVienHopDongConNguoiParams) => Promise<boolean>;
}

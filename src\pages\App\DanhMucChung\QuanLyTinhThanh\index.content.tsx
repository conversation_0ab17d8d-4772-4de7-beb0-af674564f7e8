/**
 * Components chính:
 * - Table: Hi<PERSON>n thị danh sách tỉnh thành
 * - Form: Tì<PERSON> kiếm và filter
 * - Modal: <PERSON> tiết tỉnh thành (import từ Component)
 */
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip, Card, Flex} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {PlusCircleOutlined, SearchOutlined, ClearOutlined, DeleteOutlined, EditOutlined, PlusOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";
import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {useQuanLyTinhThanhContext} from "./index.context";
import {FormTimKiemQuanLyTinhThanh, tableTinhThanhColumn, TableTinhThanhColumnDataType, TableTinhThanhColumnDataIndex, radioItemTrangThaiTinhThanhSelect,radioItemNgayApDungTinhThanhSelect, radioItemTrangThaiTinhThanhTable, DANH_SACH_MIEN_TIM_KIEM, DANH_SACH_MIEN_VIET_NAM,NGAY_AD_TAO_MOI_TINH_THANH} from "./index.configs";
import {ModalChiTietTinhThanh, IModalChiTietTinhThanhRef} from "./Component";
import "./index.default.scss";

type DataIndex = keyof TableTinhThanhColumnDataType;

const {
  ten,
  ma,
  // mien, // BE 
  ngay_ad,
  trang_thai,
} = FormTimKiemQuanLyTinhThanh;

const QuanLyTinhThanhContent: React.FC = memo(() => {
  const {listTinhThanh, loading, tongSoDong, filterParams, getChiTietTinhThanh, setFilterParams} = useQuanLyTinhThanhContext();
  const refModalChiTietTinhThanh = useRef<IModalChiTietTinhThanhRef>(null);
  const refSearchInputTable = useRef<InputRef>(null);
  const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
  const [searchedColumn, setSearchedColumn] = useState<TableTinhThanhColumnDataIndex | "">(""); //key column đang được search

  const dataTableListTinhThanh = useMemo<Array<TableTinhThanhColumnDataType>>(() => {
    try {
      const currentPage = filterParams?.trang || 1;
      const currentPageSize = (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number;
      
      const tableData = listTinhThanh.map((itemTinhThanh, index) => {
        return {
          key: itemTinhThanh.ma || index.toString(),
          sott: (currentPage - 1) * currentPageSize + index + 1, //Tính STT theo trang hiện tại
          ngay_ad: itemTinhThanh.ngay_ad,
          ma: itemTinhThanh.ma,
          ten: itemTinhThanh.ten,
          mien: itemTinhThanh.mien || "",
          stt: itemTinhThanh.stt,
          trang_thai: itemTinhThanh.trang_thai,
          ngay_tao: itemTinhThanh.ngay_tao,
          nguoi_tao: itemTinhThanh.nguoi_tao,
          ngay_cap_nhat: itemTinhThanh.ngay_cap_nhat,
          nguoi_cap_nhat: itemTinhThanh.nguoi_cap_nhat,
          trang_thai_ten: itemTinhThanh.trang_thai_ten,
        } as TableTinhThanhColumnDataType;
      });
      const arrEmptyRow: Array<TableTinhThanhColumnDataType> = fillRowTableEmpty(tableData.length, currentPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [listTinhThanh, filterParams?.trang, filterParams?.so_dong]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableTinhThanhColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableTinhThanhColumnDataIndex) => {
      clearFilters();
      setSearchTextTable("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  // chức năng search
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams) => {
    // Xử lý submit form tìm kiếm và cập nhật filter parameters
    setFilterParams({...filterParams, ...values, trang: 1, so_dong: filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize});
  };

  // xử lý click row để mở modal detail
  const handleRowClick = useCallback(async (record: TableTinhThanhColumnDataType) => {
    // Xử lý click vào row để hiển thị modal chi tiết tỉnh thành
    if (record.key.toString().includes("empty") || loading) return;
    
    try {
      if (!record.ma) return;
      
      const response = await getChiTietTinhThanh(record);
      if (response) {
        refModalChiTietTinhThanh.current?.open(response);
      }
    } catch (error) {
      // Xử lý lỗi thầm lặng, đã có message error trong provider
    }
  }, [getChiTietTinhThanh, loading]);

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderHeaderTableQuanLyTinhThanh = () => {
    return (
      <>
        <Form initialValues={{}} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum({...ngay_ad, options: radioItemNgayApDungTinhThanhSelect})}
            {renderFormInputColum(ten)}
            {renderFormInputColum(ma)}
            {renderFormInputColum({...trang_thai, options: radioItemTrangThaiTinhThanhSelect})}
            <Col span={3}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
            <Col span={3}>
              <Button
                type="primary"
                block
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  refModalChiTietTinhThanh.current?.open();
                }}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Form>
      </>
    );
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableTinhThanhColumnDataIndex, title: string): TableColumnType<TableTinhThanhColumnDataType> => ({
    /**
     *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     * @param param0
     * @returns
     * dataIndex !== "trang_thai_ten"
     */
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? filterDropdownParams => (
            <TableFilterDropdown
              ref={refSearchInputTable}
              title={title}
              dataIndex={dataIndex}
              handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
              handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
              {...filterDropdownParams}
            />
          )
        : undefined,
    /**
     * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
     * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
     * @param value : giá trị filter người dùng nhập vào
     * @param record : từng bản ghi trong dataSource
     * @returns
     */
    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    /**
     * filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
     */
    filterDropdownProps: {
      // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiTinhThanhTable : undefined,
    render: (
      text,
      record,
      //  index
    ) => {
      if (record.key?.toString().includes("empty")) return <span style={{height: '22px', display: 'inline-block'}}>&nbsp;</span>;
      
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      
      // Xử lý đặc biệt cho cột ngày áp dụng: convert số thành text
      if (dataIndex === "ngay_ad") {
        const convertNgayApDungToText = (ngayAd: number | string | null | undefined): string => {
          if (ngayAd === null || ngayAd === undefined || ngayAd === '') {
            return '';
          }
          const ngayAdStr = String(ngayAd);
          switch (ngayAdStr) {
            case '19000101':
              return '01/01/1900';
            case '20250701':
              return '01/07/2025';
            default:
              return ngayAdStr === 'null' || ngayAdStr === 'undefined' ? '' : ngayAdStr;
          }
        };
        
        const displayText = convertNgayApDungToText(text);
        return searchedColumn === dataIndex ? (
          <Highlighter searchWords={[searchTextTable]} textToHighlight={displayText} />
        ) : (
          displayText
        );
      }
      
      // Xử lý hiển thị cột miền: chuyển mã (BAC, TRUNG, NAM) thành tên
      if (dataIndex === "mien") {
        if (!text) return "";
        
        const mienInfo = DANH_SACH_MIEN_VIET_NAM.find(item => item.ma === text);
        const displayText = mienInfo ? mienInfo.ten : text;
        
        return searchedColumn === dataIndex ? (
          <Highlighter searchWords={[searchTextTable]} textToHighlight={displayText.toString()} />
        ) : (
          displayText
        );
      }
      
      // Xử lý hiển thị mặc định cho các cột khác
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  return (
    <div id={ID_PAGE.QUAN_LY_TINH_THANH} className="[&_.ant-space]:w-full">
      <Table<TableTinhThanhColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListTinhThanh} //mảng dữ liệu record được hiển thị
        columns={
          tableTinhThanhColumn?.map(item => {
            //Nếu là cột sott thì không hiển thị search, các cột khác đều có search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableTinhThanhColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          current: filterParams?.trang || 1,
          pageSize: (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            setFilterParams({...filterParams, trang: page, so_dong: pageSize});
          },
        }} //cấu hình phân trang
        title={renderHeaderTableQuanLyTinhThanh} //render header search form
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })} //xử lý click row
      />
      <ModalChiTietTinhThanh 
        ref={refModalChiTietTinhThanh} 
        onAfterSave={() => {
          // Refresh data sau khi save
          setFilterParams({...filterParams});
        }} 
      />
    </div>
  );
});

export default QuanLyTinhThanhContent;

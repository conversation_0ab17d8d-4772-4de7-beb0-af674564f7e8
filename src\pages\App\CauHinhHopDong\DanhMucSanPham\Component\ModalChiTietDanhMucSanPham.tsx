import {forwardRef, memo, useEffect, useImperativeHandle, useState} from "react";
import FormChiTietDanhMucSanPham, {IModalChiTietDanhMucSanPhamRef, Props, TRANG_THAI} from "./index.configs";
import {Col, Flex, Form, Modal, Row} from "antd";
import {useDanhMucSanPhamContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal} from "@src/components";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
const {ma_doi_tac_ql, ma, ten, nv, stt, trang_thai} = FormChiTietDanhMucSanPham;

const ModalChiTietDanhMucSanPhamComponent = forwardRef<IModalChiTietDanhMucSanPhamRef, Props>(({listDoiTac}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucSanPham?: CommonExecute.Execute.IDanhMucSanPham) => {
      setIsOpen(true);
      if (dataDanhMucSanPham) setChiTietDanhMucSanPham(dataDanhMucSanPham);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucSanPham, setChiTietDanhMucSanPham] = useState<CommonExecute.Execute.IDanhMucSanPham | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {loading, onUpdateDanhMucSanPham, filterParams, setFilterParams, danhSachSanPhamPhanTrang, listNghiepVu} = useDanhMucSanPhamContext();

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  useEffect(() => {
    if (chiTietDanhMucSanPham) {
      const arrFormData = [];
      for (const key in chiTietDanhMucSanPham) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IDanhMucSanPham,
          value: chiTietDanhMucSanPham[key as keyof CommonExecute.Execute.IDanhMucSanPham],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucSanPham, form]);
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = () => {
    setIsOpen(false);
    setChiTietDanhMucSanPham(null);
    form.resetFields();
    setFilterParams(filterParams);
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDanhMucSanPhamParams = form.getFieldsValue(); //lấy ra values của form
      // if (!chiTietDanhMucSanPham) {
      //   // console.log("check list dai ly", listDaiLy);
      //   for (let i = 0; i < danhSachSanPhamPhanTrang.length; i++) {
      //     if (danhSachSanPhamPhanTrang[i].ma === values.ma && danhSachSanPhamPhanTrang[i].ma_doi_tac_ql === values.ma_doi_tac_ql) {
      //       form.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã sản phẩm đã tổn tại!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      const response = await onUpdateDanhMucSanPham(values);
      console.log("check respon ", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        setIsOpen(false);
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //render footer
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
        {/* <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle="Lưu"
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        /> */}
      </Form.Item>
    );
  };
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, disabled: chiTietDanhMucSanPham ? true : false})}
        {renderFormInputColum({...nv, options: listNghiepVu, disabled: chiTietDanhMucSanPham ? true : false})}
        {renderFormInputColum({...ma, disabled: chiTietDanhMucSanPham ? true : false})}
      </Row>
      <Row gutter={16}>
        {renderFormInputColum({...ten})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
        {renderFormInputColum({...stt})}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDanhMucSanPham ? `Chi tiết Sản phẩm ${chiTietDanhMucSanPham.ten}` : "Tạo mới sản phẩm"}
            trang_thai_ten={chiTietDanhMucSanPham?.trang_thai_ten}
            trang_thai={chiTietDanhMucSanPham?.trang_thai}
          />
        }
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "55%",
          sm: "55%",
          md: "55%",
          lg: "55%",
          xl: "55%",
          xxl: "55%",
        }}
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={renderFooter}
        className="[&_.ant-space]:w-full">
        {renderForm()}
      </Modal>
    </Flex>
  );
});
ModalChiTietDanhMucSanPhamComponent.displayName = "ModalChiTietDanhMucSanPhamComponent";
export const ModalChiTietDanhMucSanPham = memo(ModalChiTietDanhMucSanPhamComponent, isEqual);

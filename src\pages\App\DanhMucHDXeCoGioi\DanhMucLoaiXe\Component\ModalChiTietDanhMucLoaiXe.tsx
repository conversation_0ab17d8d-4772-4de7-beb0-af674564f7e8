import {forwardRef, memo, useEffect, useImperativeHandle, useState, useMemo} from "react";
import FormChiTietDanhMucLoaiXe, {IModalChiTietDanhMucLoaiXeRef, Props, TRANG_THAI_CHI_TIET_LOAI_XE, NGHIEP_VU_OPTIONS} from "./index.configs";
import {Col, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {useDanhMucLoaiXeContext} from "../index.context";

const {ma, ten, nv, stt, trang_thai} = FormChiTietDanhMucLoaiXe;

const ModalChiTietDanhMucLoaiXeComponent = forwardRef<IModalChiTietDanhMucLoaiXeRef, Props>(({onAfterSave}: Props, ref) => {
  // Lấy data và functions từ context
  const {onUpdateDanhMucLoaiXe, loading} = useDanhMucLoaiXeContext();

  useImperativeHandle(ref, () => ({
    open: (dataChiTietLoaiXe?: CommonExecute.Execute.IChiTietDanhMucLoaiXe | null) => {
      setIsOpen(true);
      if (dataChiTietLoaiXe) {
        setChiTietDanhMucLoaiXe(dataChiTietLoaiXe);
      } else {
        setChiTietDanhMucLoaiXe(null);
      }
    },
    close: () => closeModal(),
  }));

  const [chiTietDanhMucLoaiXe, setChiTietDanhMucLoaiXe] = useState<CommonExecute.Execute.IChiTietDanhMucLoaiXe | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);

  /**
   * LẤY OPTIONS TRẠNG THÁI CHO MODAL
   */
  const trangThaiOptionsForModal = useMemo(() => {
    return TRANG_THAI_CHI_TIET_LOAI_XE.map(item => ({
      ten: item.ten,
      ma: item.ma,
    }));
  }, []);

  // Set form data khi có chi tiết
  useEffect(() => {
    if (chiTietDanhMucLoaiXe) {
      // Set form fields
      const formData: any = {
        ma: chiTietDanhMucLoaiXe.ma,
        ten: chiTietDanhMucLoaiXe.ten,
        stt: chiTietDanhMucLoaiXe.stt,
        trang_thai: chiTietDanhMucLoaiXe.trang_thai,
      };

      // Xử lý trường nghiệp vụ
      const nv = (chiTietDanhMucLoaiXe as any).nv || (chiTietDanhMucLoaiXe as any).ten_nv;
      if (nv) {
        formData.nv = nv;
      }

      form.setFieldsValue(formData);
    } else {
      // Reset form cho trường hợp thêm mới
      form.resetFields();
    }
  }, [chiTietDanhMucLoaiXe, form]);

  // Validate form để enable/disable nút Lưu
  useEffect(() => {
    form
      .validateFields({validateOnly: true})
      .then(() => {
        setDisableSubmit(false);
      })
      .catch(() => {
        setDisableSubmit(true);
      });
  }, [form, formValues]);

  const closeModal = () => {
    setIsOpen(false);
    setChiTietDanhMucLoaiXe(null);
    form.resetFields();
  };

  const renderFormColumn = (props: any) => (
    <div className="form-item-compact">
      <FormInput {...props} />
    </div>
  );

  const onConfirm = async () => {
    try {
      // Validate form trước khi submit
      await form.validateFields();

      // Lấy form values
      const values = form.getFieldsValue();

      // Validate required fields theo interface ICapNhatDanhMucLoaiXeParams
      if (!values.ma) {
        throw new Error("Mã loại xe là bắt buộc");
      }
      if (!values.ten) {
        throw new Error("Tên loại xe là bắt buộc");
      }
      if (!values.nv) {
        throw new Error("Nghiệp vụ là bắt buộc");
      }
      if (values.stt === undefined || values.stt === null) {
        throw new Error("Số thứ tự là bắt buộc");
      }
      if (!values.trang_thai) {
        throw new Error("Trạng thái là bắt buộc");
      }

      // Chuẩn bị params theo interface ICapNhatDanhMucLoaiXeParams
      const params: ReactQuery.ICapNhatDanhMucLoaiXeParams = {
        ma: values.ma,
        ten: values.ten,
        nv: values.nv,
        stt: Number(values.stt),
        trang_thai: values.trang_thai,
      };

      
      const isSuccess = await onUpdateDanhMucLoaiXe(params);// Gọi API cập nhật
      
      if (isSuccess) {
        // Đóng modal và callback
        closeModal();
        onAfterSave?.();
      }
    } catch (error: any) {
      console.log("onConfirm error", error);
    }
  };

  // Render form theo layout DanhMucChiNhanhNganHang
  const renderForm = () => (
    <Form 
      form={form} 
      layout="vertical" 
      className="mt-2"
      style={{
        "--form-item-margin-bottom": "12px"
      } as React.CSSProperties}
    >
      {/* Row 1: Mã loại xe + Tên loại xe */}
      <Row gutter={16} style={{ marginBottom: "1px" }}>
        <Col span={10}>{renderFormColumn({...ma, disabled: chiTietDanhMucLoaiXe ? true : false})}</Col>
        <Col span={14}>{renderFormColumn({...ten})}</Col>
      </Row>
      
      {/* Row 2: Nghiệp vụ + Số thứ tự + Trạng thái */}
      <Row gutter={16} style={{ marginBottom: "1px" }}>
        <Col span={10}>{renderFormColumn({...nv, options: NGHIEP_VU_OPTIONS, disabled: chiTietDanhMucLoaiXe ? true : false})}</Col>
        <Col span={5}>{renderFormColumn({...stt})}</Col>
        <Col span={9}>{renderFormColumn({...trang_thai, options: trangThaiOptionsForModal})}</Col>
      </Row>
    </Form>
  );

  // Render footer cho modal
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };

  // ===== MAIN RENDER =====
  return (
    <Modal
      title={
        <HeaderModal
          title={chiTietDanhMucLoaiXe ? `${chiTietDanhMucLoaiXe.ten}` : "Tạo mới loại xe"}
          trang_thai_ten={chiTietDanhMucLoaiXe?.trang_thai_ten}
          trang_thai={chiTietDanhMucLoaiXe?.trang_thai}
        />
      }
      open={isOpen}
      onOk={() => closeModal()}
      onCancel={() => closeModal()}
      maskClosable={false}
      width={750}
      styles={{
        body: {
          paddingTop: "8px",
          paddingBottom: "16px",
        },
      }}
      footer={renderFooter()}>
      {renderForm()}
    </Modal>
  );
});

ModalChiTietDanhMucLoaiXeComponent.displayName = "ModalChiTietDanhMucLoaiXeComponent";

export const ModalChiTietDanhMucLoaiXe = memo(ModalChiTietDanhMucLoaiXeComponent, isEqual);
